# 后端代码规范文档

## 1. 项目结构规范

### 1.1 包结构
```
eve.sys.modular.{业务模块}
├── controller/     # 控制器层
├── entity/        # 实体类
├── mapper/        # 数据访问层
│   └── mapping/   # MyBatis XML映射文件
└── service/       # 服务层
    └── impl/      # 服务实现类
```

## 2. Controller层规范

### 2.1 基本注解要求
- **@RestController**: 所有Controller类必须使用此注解
- **@RequestMapping**: 路径命名规则为 `/{实体类名首字母小写}`
  - 示例：`TestShareController` → `@RequestMapping("/testShare")`

### 2.2 接口规范
Controller必须包含以下6个基本接口：

| 接口名 | 请求方式 | 功能描述 | 日志类型 |
|--------|----------|----------|----------|
| `/pageList` | POST | 分页列表查询 | QUERY |
| `/list` | POST | 列表查询 | QUERY |
| `/get` | POST | 根据ID查询 | QUERY |
| `/add` | POST | 新增 | ADD |
| `/update` | POST | 更新 | UPDATE |
| `/delete` | POST | 删除 | DELETE |

### 2.3 注解使用规范
```java
@PostMapping("/pageList")
@BusinessLog(title = "分享-分页列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
public ResponseData pageList(@RequestBody TestShare param) {
    return new SuccessResponseData(shareService.pageList(param));
}
```

**要求：**
- 所有接口统一使用 **POST** 请求
- 必须添加 `@BusinessLog` 注解记录操作日志
- 参数统一使用 `@RequestBody` 接收JSON格式数据
- 注入Service使用 `@Resource` 注解

## 3. Entity层规范

### 3.1 基本注解要求
```java
@Data
@TableName("TEST_SHARE")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TestShare extends BaseEntity {
    // 实体内容
}
```

### 3.2 继承规范
- **必须继承 `BaseEntity`**，包含以下基础字段：
  - 创建人、创建时间
  - 修改人、修改时间
  - 删除标识等

### 3.3 表名命名规范
- 格式：`{应用名}_{业务名}`
- 示例：`TEST_SHARE` (TEST应用 + SHARE业务)
- 使用 `@TableName` 注解指定表名

### 3.4 主键规范
```java
/**
 * 主键
 */
@TableId(value = "id", type = IdType.ASSIGN_ID)
private Long id;
```
- ID字段类型必须为 `Long`
- 必须使用固定注解 `@TableId(value = "id", type = IdType.ASSIGN_ID)`

### 3.5 字段注释规范
- **所有字段都必须添加注释**
- 枚举字段需要详细描述中英文对照
- 示例：`类型 （collect 收藏 processTop 日历寿命计划置顶）`

## 4. Mapper层规范

### 4.1 接口定义
```java
public interface TestShareMapper extends BaseMapper<TestShare> {
    // 继承MyBatis Plus的BaseMapper
}
```

### 4.2 XML映射文件
- 对应的XML文件必须放在 `mapping` 目录下
- 文件名与Mapper接口名保持一致

## 5. Service层规范

### 5.1 接口定义
```java
public interface ITestShareService extends IService<TestShare> {
    PageResult<TestShare> pageList(TestShare param);
    List<TestShare> list(TestShare param);
    Boolean add(TestShare param);
    Boolean delete(TestShare param);
    Boolean update(TestShare param);
    TestShare get(TestShare param);
}
```

### 5.2 实现类规范
```java
@Service
@Slf4j
public class TestShareServiceImpl extends ServiceImpl<TestShareMapper, TestShare> 
    implements ITestShareService {
    // 实现内容
}
```

**基本要求：**
- 必须使用 `@Service` 注解
- 必须添加 `@Slf4j` 用于日志记录
- 必须继承 `ServiceImpl<Mapper, Entity>`
- 必须包含6个基础接口的实现

## 6. 业务逻辑规范

### 6.1 查询接口规范
```java
/**
 * 测试分享分页查询
 */
@Override
public PageResult<TestShare> pageList(TestShare param) {
    LambdaQueryWrapper<TestShare> queryWrapper = new LambdaQueryWrapper<>();
    
    // 必须判空
    if(StrUtil.isNotBlank(param.getUserName())){
        queryWrapper.eq(TestShare::getUserName, param.getUserName());
    }
    
    queryWrapper.orderByDesc(TestShare::getCreateTime);
    Page<TestShare> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
    return new PageResult<>(page);
}
```

**要求：**
- 查询条件必须判空：`if(StrUtil.isNotBlank(param.getField()))`
- 默认按创建时间倒序排列

### 6.2 新增接口规范
```java
/**
 * 测试分享新增
 */
@Override
public Boolean add(TestShare param) {
    // 重复性校验
    LambdaQueryWrapper<TestShare> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(TestShare::getReportId, param.getId());
    queryWrapper.eq(TestShare::getUserAccount, param.getUserAccount());
    
    int count = this.count(queryWrapper);
    if(count > 0){
        log.info("重复分享", JSON.toJSONString(param));
        throw new ServiceException(400, "请勿重复分享");
    }
    
    return this.save(param);
}
```

**要求：**
- 根据业务需要添加重复性校验
- 使用日志记录关键操作
- 抛出业务异常时使用 `ServiceException`

### 6.3 删除/更新/查询接口规范
```java
/**
 * 测试分享删除
 */
@Override
public Boolean delete(TestShare param) {
    if(ObjectUtil.isEmpty(param.getId())){
        throw new ServiceException(400, "ID必填");
    }
    return removeById(param.getId());
}

/**
 * 测试分享更新
 */
@Override
public Boolean update(TestShare param) {
    if(ObjectUtil.isEmpty(param.getId())){
        throw new ServiceException(400, "ID必填");
    }
    // 只传递可更新字段，防止错误更新
    TestShare updateEntity = TestShare.builder()
            .id(param.getId())
            .reportId(param.getReportId())
            .userAccount(param.getUserAccount())
            .build();
    return updateById(updateEntity);
}
```

**要求：**
- 必须校验ID是否为空
- 更新操作只传递可更新字段，防止误更新
- 可根据业务需要编写多个专门的更新接口

## 7. 注释规范

### 7.1 类注释
- 每个类都必须添加详细的功能说明注释
- 说明类的职责和使用规范

### 7.2 方法注释
- 每个方法必须添加业务功能描述
- 格式：`/**业务模块+功能描述*/`
- 示例：`/**测试分享分页查询*/`

## 8. 日志规范

### 8.1 日志注解
- Controller层：使用 `@BusinessLog` 记录业务操作日志
- Service层：使用 `@Slf4j` 记录技术日志

### 8.2 日志内容
- 记录关键业务操作
- 记录异常情况和错误信息
- 使用JSON格式记录复杂对象

## 9. 异常处理规范

### 9.1 业务异常
```java
throw new ServiceException(400, "错误信息");
```

### 9.2 参数校验
- 必填参数校验：ID、关键业务字段
- 字符串判空：使用 `StrUtil.isNotBlank()`
- 对象判空：使用 `ObjectUtil.isEmpty()`

## 10. 代码质量要求

1. **命名规范**：类名、方法名、变量名使用驼峰命名法
2. **代码整洁**：保持代码简洁，逻辑清晰
3. **注释完整**：关键业务逻辑必须有注释说明
4. **异常处理**：合理处理各种异常情况
5. **日志记录**：记录必要的业务和技术日志

---

*本规范基于项目实际代码分析生成，请严格遵守以确保代码质量和项目一致性。*

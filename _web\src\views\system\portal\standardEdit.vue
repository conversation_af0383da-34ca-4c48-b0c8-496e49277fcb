<template>
	<div class="container relative">
		<div class="left-container">
			<a-tabs default-active-key="1" @change="callback">
				<a-tab-pane key="1" :tab="isView ? '查阅信息' : '编辑信息'">
					<div>
						<div class="step">
							<a-steps :current="current">
								<a-step v-for="item in steps" :key="item.title" :title="item.title" />
							</a-steps>
						</div>
						<div class="wrapper" v-if="current == 0">
							<!--<div class="f-16 fw-6 mt10">{{isEdit ? '更新' : '新增'}}信息</div>-->
							<div class="filling-warpper flex mt-20">
								<div class="filling-box-fileTile" v-if="['10513'].indexOf(form.fileTypeId) == -1">
									<div class="label">文档标题:</div>
									<span v-if="form.fileTypeId != '22704'" class="grey"> {{ form.fileTitle ?
										form.fileTitle : '自动生成' }}</span>
									<a-input v-else v-model="tempFileTitle"
										placeholder="复制检测报告文档已有的文档名称 例：[产品规范] G26E-V1.1-001 B2样产品规范_PBRI-G26E-V1.1-D07-04_C版_20240615"
										:disabled="isView" style="width:calc(100% - 100px);" />
								</div>

								<div class="filling-box">
									<div class="label">所属分类:</div>
									<a-select v-model="form.fileTypeId" placeholder="请选择所属分类" :disabled="isView"
										style="width:calc(100% - 100px);" @change="handleFileTypeIdChange">
										<a-select-option v-for="(item) in fileTypeList" :key="item.code"
											:value="item.id">
											{{ item.customvalue }}
										</a-select-option>
									</a-select>
								</div>

								<div class="filling-box">
									<div class="label">产品名称:</div>
									<a-select v-model="form.issueId" option-filter-prop="children" placeholder="请选择产品名称"
										style="width:calc(100% - 100px);" :disabled="isView || isValidationTest"
										allowClear show-search @change="handleProductChange">
										<a-select-option v-for="(item, i) in products" :value="item.issueId" :key="i">
											{{ item.productName }}
										</a-select-option>
									</a-select>
								</div>
								<!--<div class="filling-box">-->
								<!--	<div class="label">项目名称:</div>-->
								<!--	<a-select v-model="form.issueId" option-filter-prop="children"-->
								<!--		style="width:calc(100% - 100px);" :disabled="true" allowClear show-search-->
								<!--		@change="handleProductChange">-->
								<!--		<a-select-option v-for="(item, i) in products" :value="item.issueId" :key="i">-->
								<!--			{{ item.projectName }}-->
								<!--		</a-select-option>-->
								<!--	</a-select>-->
								<!--</div>-->
								<div class="filling-box">
									<div class="label">项目名称:</div>
									<a-select v-model="form.issueId" option-filter-prop="children" placeholder="请选择项目名称"
										style="width:calc(100% - 100px);" :disabled="isView || isValidationTest"
										allowClear show-search @change="handleProductChange">
										<a-select-option v-for="(item, i) in projects" :value="item.issueId" :key="i">
											{{ item.projectName }}
										</a-select-option>
									</a-select>
								</div>

								<div class="filling-box" v-if="['22713'].indexOf(form.fileTypeId) == -1">
									<div class="label">技术状态:</div>
									<a-select v-model="form.techStatusId" placeholder="请选择技术状态"
										style="width:calc(100% - 100px);"
										:options="techStatusList.map(e => ({ value: e.id, label: e.customvalue }))"
										:disabled="isView || isValidationTest" allowClear showSearch />
								</div>

								<div class="filling-box" v-if="['10511', '10512','22713'].indexOf(form.fileTypeId) == -1">
									<div class="label">技术状态编号:</div>
									<a-input-number :min="0" v-model="form.techStatusNo" placeholder="请输入技术状态编号"
										:disabled="isView || isValidationTest" style="width:calc(100% - 100px);" />
								</div>

								<div class="filling-box" style="width: calc(200% / 3);" v-if="['22713','10513'].indexOf(form.fileTypeId) == -1">
									<div class="label">文档编号:</div>
									<a-input v-model="form.fileNo" placeholder="请输入文档编号" :disabled="isView"
										style="width:calc(100% - 100px);" />
								</div>

								<div class="filling-box" v-if="['10513'].indexOf(form.fileTypeId) == -1">
									<div class="label"> 文档版本:</div>
									<a-select v-model="form.fileVersion" placeholder="请选择版本" :disabled="isView"
										style="width:calc(100% - 100px);">
										<a-select-option v-for="item in versionList" :key="item" :value="item">
											{{ item }}
										</a-select-option>
									</a-select>
								</div>

								<div class="filling-box">
									<div class="label"> 文档作者:</div>
									<a-select v-model="form.accountRemark" placeholder="请选择文档作者"
										style="width:calc(100% - 100px);" @change="handleChangeRemark"
										@search="handleSearch" :disabled="isView" allowClear showSearch
										:options="userList.map(item => ({ label: item.accountRemark, value: item.accountRemark }))">
									</a-select>
								</div>

								<div class="filling-box">
									<div class="label"> 文档作者部门:</div>
									<a-select v-model="form.deptId" placeholder="请选择文档作者部门" :disabled="isView"
										style="width:calc(100% - 100px);">
										<a-select-option v-for="(item, i) in this.projectDepartmentList" :key="i"
											:value="item.id">{{ item.customvalue }}</a-select-option>
									</a-select>
								</div>

								<div class="filling-box">
									<div class="label">生效日期:</div>
									<a-date-picker v-model="form.startTime" format='YYYY-MM-DD' :disabled="isView"
										style="width:calc(100% - 100px);"
										@change="(date, dateString) => handleChangeDate(date, dateString, 'startTime')" />
								</div>

								<div class="filling-box">
									<div class="label">失效日期:</div>
									<a-date-picker v-model="form.loseTime" format='YYYY-MM-DD' :disabled="isView"
										style="width:calc(100% - 100px);"
										@change="(date, dateString) => handleChangeDate(date, dateString, 'loseTime')" />
								</div>

								<div class="filling-box" v-if="['10514', '22703'].indexOf(form.fileTypeId) != -1">
									<div class="label">文档格式:</div>
									<a-select v-model="form.docType" placeholder="请选择文档格式" :disabled="isView"
										style="width:calc(100% - 100px);">
										<a-select-option v-for="(item, i) in getDict('doc_formate')" :value="item.code"
											:key="i">
											{{ item.name }}
										</a-select-option>
									</a-select>
								</div>

                <div v-if="['10513'].indexOf(form.fileTypeId) !== -1" class="filling-box">
                  <div class="label">物料编号:</div>
                  <a-input v-model="form.materialNo" placeholder="请输入物料编号" :disabled="isView" style="width:calc(100% - 100px);" />
                </div>
                <div v-if="['10513'].indexOf(form.fileTypeId) !== -1" class="filling-box">
                  <div class="label">测试类型:</div>
                  <a-select v-model="form.additionalTypeId" placeholder="请选择测试类型" showSearch optionFilterProp="children" :disabled="isView" style="width:calc(100% - 100px);">
                    <a-select-option v-for="item in testTypeList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </div>
                <div v-if="['10513','10515'].indexOf(form.fileTypeId) !== -1" class="filling-box" style="width: 100%;">
                  <div class="label">{{ form.fileTypeId === '10515' ? 'PPT:' : '实物图:' }}</div>
                  <a-upload-dragger :disabled="isView" style="width: calc(100% - 100px);"
                            :headers="headers" :action="postUrl" :fileList="additionalFileList"
                            :accept="additionalAcceptType" :beforeUpload="(file) => beforeUpload(file, additionalAcceptType)"
                            @preview="previewFile" @change="handleUploadChange($event)">
                    <p class="ant-upload-drag-icon"><a-icon type="inbox" /></p>
                    <p class="ant-upload-text">点击上传{{ getAcceptName(additionalAcceptType) }}</p>
                  </a-upload-dragger>
                </div>


								<div class="filling-box" style="width: calc(300% / 3);">
									<div class="label">{{ ['10513'].indexOf(form.fileTypeId) !== -1 ? '图纸:' : form.fileTypeId === '10515' ? 'PDF:' : '附件:' }}</div>
									<a-upload-dragger name="file" :fileList="fileList"
										:remove="handleDownloadFileRemove" :headers="headers" :showUploadList="true"
										:multiple="true" :disabled="isView" style="width: calc(100% - 100px);"
                    :accept="acceptType" :beforeUpload="(file) => beforeUpload(file, acceptType)"
										:customRequest="$event => handleFileUpload($event)" @preview="previewFile">
										<p class="ant-upload-drag-icon">
											<a-icon type="inbox" />
										</p>
										<p class="ant-upload-text">
											点击上传{{ getAcceptName(acceptType) }}
										</p>
									</a-upload-dragger>
								</div>

                <div v-if="['10513'].indexOf(form.fileTypeId) !== -1" class="filling-box" style="width: calc((100% / 3) * 3);">
                  <div class="label">物料描述:</div>
                  <a-textarea v-model="form.content" placeholder="请输入物料描述" :disabled="isView" style="width:calc(100% - 100px); height: 130px;" />
                </div>
								<div v-else class="filling-box" style="width: calc((100% / 3) * 3);">
									<div class="label">内容 <a-tooltip title="例：G26E-V1.1 B样产品规范首版发行。"
											:overlayStyle="{ maxWidth: '300px' }"><a-icon type="question-circle"
												style="color: #1890ff;" /></a-tooltip> :</div>
									<a-textarea v-model="form.content" placeholder="例：G26E-V1.1 B样产品规范首版发行。"
										:disabled="isView" style="width:calc(100% - 100px); height: 130px;" />
								</div>
								<div class="quill-upload" style="display: none;">
									<a-upload name="file" :headers="headers" :action="`/api/sysFileInfo/upload`"
										:showUploadList="false"
										:customRequest="$event => handleFileUpload($event, 'content')">
										<a-button type="link" style="color:#1890ff;" class="choose-btn">上传</a-button>
									</a-upload>
								</div>
							</div>

						</div>
						<div class="wrapper" v-if="current == 1">
							<div class="filling-warpper flex mt-20">
								<div class="filling-box">
									<div class="label"> 审核经办人:</div>
									<a-select v-model="form.accountCheck" placeholder="请选择文档作者"
										style="width:calc(100% - 100px);" @change="handleChangeCheck"
										@search="handleSearch" :disabled="isView" allowClear showSearch
										:options="userList.map(item => ({ label: item.accountRemark, value: item.accountRemark }))">
									</a-select>
								</div>
							</div>
						</div>
						<div class="flex mt10" style="justify-content: flex-end;">
							<a-button class="mr10" v-if="current > 0" size="small" @click="prev">上一步</a-button>
							<a-button class="mr10" v-if="current < steps.length - 1" size="small" type="primary"
								@click="next">下一步</a-button>
							<a-button class="mr10" v-if="current == 1" size="small" type="primary" :loading="btnLoading"
								@click="handleSubmit" :disabled="isView">提交</a-button>
							<a-button class="mr10" size="small" type="primary" @click="editSave" :loading="saveLoading"
								:disabled="isView">暂存</a-button>
							<a-button size="small" @click="handleCancel">取 消</a-button>
						</div>
					</div>
				</a-tab-pane>
			</a-tabs>
		</div>
		<div class="right-container">
			<a-tabs default-active-key="1" @change="callback">
				<a-tab-pane key="1" tab="文档信息">
					<div class="filling-warpper flex">
						<div>
							<div class="tip-box-content">
								<div class="tip-label">录入者:&nbsp;&nbsp; {{ rightForm.createUser }}</div>
							</div>
							<div class="tip-box-content">
								<div class="tip-label">所属部门:&nbsp;&nbsp; {{ rightForm.dept }}</div>
							</div>
							<div class="tip-box-content">
								<div class="tip-label">录入时间:&nbsp;&nbsp;{{ rightForm.createTime }}</div>
							</div>
							<div class="tip-box-content">
								<div class="tip-label">录入编号:&nbsp;&nbsp;<span class="grey">{{ rightForm.createNo
								}}</span></div>
							</div>
							<div class="tip-box-content">
								<div class="tip-label">文档状态:&nbsp;&nbsp;<span class="grey">{{
									rightForm.fileStatus }}</span></div>
							</div>
						</div>

					</div>
				</a-tab-pane>
			</a-tabs>

		</div>
		<a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible3"
			@close="visible3 = false">
			<iframe :src="pdfUrl + '#view=FitH,top&'" width="100%" height="100%"></iframe>
		</a-drawer>
	</div>

</template>
<script>
import { getProductList } from "@/api/modular/system/limsManager";
import { add, getById, edit, getProducts, summitJIRA } from "@/api/modular/system/standardManage"
import { getUserLists, sysUserGetLeader } from "@/api/modular/system/userManage"
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import pbiPreview from '@/components/pageTool/components/pbiPreview.vue'
import { mixin } from "@/views/system/index/mixin/index"
import Vue from "vue";
import { mapGetters } from 'vuex'
import { getDepartmentOptionList, getJiraOptionList } from "@/api/modular/system/qualityManage";
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
export default {
	props: {
		options: {
			type: Array,
			default: () => [],
		},
	},
	components: {
		pbiPreview
	},
	mixins: [mixin],
	data() {
		return {
			isValidationTest: false,
			testRepresentAccount: '',
			testRepresentName: '',
			tempFileTitle: '',
			isView: true,
			visible3: false,
			fileList: [],
			id: '',
			btnLoading: false,
			saveLoading: false,
			isEdit: false,
			previewVisible: false,
			form: {
				productName: '',
				startTime: '',
				loseTime: '',
				fileVersion: '',
				fileAuthorRemark: '',
				accountRemark: '',
				content: '',
				fileNo: '',
				accountCheck: '',
			},
			versionList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
			productDepartmentList: [{ label: '动力圆柱电池研究所', value: '动力圆柱电池研究所' }, { label: '方形电池研究所', value: '方形电池研究所' }, { label: '新型电池研究所', value: '新型电池研究所' }, { label: 'V型圆柱电池研究所', value: 'V型圆柱电池研究所' }, { label: '动力电池研究所', value: '动力电池研究所' }, { label: '储能电池研究所', value: '储能电池研究所' }],
			headers: {
				Authorization: 'Bearer ' + Vue.ls.get('Access-Token')
			},
      postUrl: '/api/sysFileInfo/uploadfile',
			allProjects: [],
			products: [],
			projectOptionsObj: {},
			projects: [],
			fileStatusList: [],
			current: 0,
			steps: [
				{
					title: '信息填写',
					content: '',
				},
				{
					title: '审核',
					content: '',
				}
			],
			firstLoadForDate: true,
			pdfUrl: '',
			userList: [],
			rightForm: {
				dept: '',
			},
			projectDepartmentList: [],
			techStatusList: [],
			fileTypeList: [],
      testTypeList: [],
      additionalFileList: [],
    }
	},
	created() {

		this.getProducts()
		this.id = this.$route.query.id
		this.isView = this.$route.query.opera == 'view'
		if (this.id !== undefined) {
		} else {
			this.form.fileTitle = null
			this.rightForm.createUser = this.userInfo.name
			this.rightForm.dept = "-"
			this.rightForm.createTime = this.getFormattedDateTime();
			this.rightForm.fileStatus = '草稿'
			this.rightForm.fileStatusId = '0'
			const param = { searchValue: this.userInfo.account }
			getUserLists(param).then(res => {
				if (res.data && res.data.rows && res.data.rows[0]?.sysEmpInfo) {
					this.rightForm.dept = res.data.rows[0]?.sysEmpInfo?.orgName ?? ''
				}
			})
		}
    // 工装测试类型
    const testTypeDicts = this.getDict('standard_test_type');
    this.testTypeList = testTypeDicts.map(item => {
      return {value: item.code, label: item.name}
    })
		this.getJiraOptionList()
	},
	computed: {
		...mapGetters(['userInfo']),
    acceptType() {
      const type = this.form.fileTypeId;
      if (type === '10515') return '.pdf';           // 测试报告限制上传PDF
      return null; // 其他情况不限制
    },
    additionalAcceptType() {
      const type = this.form.fileTypeId;
      if (type === '10513') return 'image/*';        // 工装夹具图纸限制上传图片
      if (type === '10515') return '.ppt,.pptx';     // 测试报告限制上传PPT
      return null; // 其他情况不限制
    },
	},
	methods: {
		getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
		previewFile(record) {
			if (!record.id) {
				const urlObj = new URL('https://example.com' + record.url); // 需要完整的URL才能创建URL对象
				const params = new URLSearchParams(urlObj.search);
				record.id = params.get('id');
			}
			// 如果是可预览的
			if (['.xbm', '.tif', '.pjp', '.svgz', '.jpg', '.jpeg', '.ico', '.tiff', '.gif', '.svg', '.jfif', '.webp', '.png', '.bmp', '.pjpeg', '.avif', '.pdf'].some(someItem => { return typeof record.name === 'string' && record.name.indexOf(someItem) !== -1 })) {
				this.visible3 = true
				this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + record.id + "#navpanes=0"
			} else {
				// 不可预览就下载
				const a = document.createElement('a')
				a.style.display = 'none'
				a.href = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + record.id + "#navpanes=0"
				a.download = record.name
				a.click()
			}
		},
		getJiraOptionList() {
			//部门
			getDepartmentOptionList({ fieldName: 'PBI_TestVerifyDepartment' }).then(res => {
				this.projectDepartmentList = res.data
				this.projectDepartmentList.push({
					id: 18878,
					customvalue: '第六实验室（JM）'
				})
			})
			//技术状态
			getJiraOptionList({ fieldName: 'techStatus' }).then(res => {
				this.techStatusList = res.data;
			})
			//所属分类
			getJiraOptionList({ fieldName: 'fileType' }).then(res => {
				console.log(res.data);
				this.fileTypeList = res.data;
			})
		},
		getProducts() {
			getProductList({}).then(res => {
				this.loading = true
				if (!res.success) {
					return
				}
				this.allProjects = res.data
				this.products = res.data.filter(filterItem => filterItem.productOrProject == 1)
				this.projectOptionsObj = {}
				this.products.forEach(item => {
					this.projectOptionsObj[item.productName] = res.data.filter(filterItem => filterItem.productName == item.productName)
				})
				if (this.$route.query.issueId) {
					this.form.issueId = this.$route.query.issueId
					this.handleProductChange(this.form.issueId)
					this.form.fileTitle = '';
					let productName = res.data.find(item => item.issueId == this.form.issueId)?.productName
					if (productName) {
						this.form.fileTitle = this.form.fileTitle + productName;
					}
					if (this.form.fileVersion) {
						this.form.fileTitle = this.form.fileTitle + "_" + this.form.fileVersion + "版_";
					}
				}
				if (this.$route.query.id) {
					this.id = this.$route.query.id
					this.getDetail()
				}

				// 从鉴定测试管理记录跳转进入，需赋值测试代表/技术状态/技术状态编号
				if (this.$route.query.testRepresentAccount && this.$route.query.techStatusId) {
					this.isValidationTest = true
					this.testRepresentAccount = this.$route.query.testRepresentAccount
					this.testRepresentName = this.$route.query.testRepresentName
					this.form.accountRemark = ''
					if (this.$route.query.techStatusNo !== undefined && this.$route.query.techStatusNo !== null) {
						this.form.techStatusNo = this.$route.query.techStatusNo
					}
					this.$set(this.form, 'techStatusId', this.$route.query.techStatusId)
				}

			}).finally(() => {
				this.loading = false
			})
		},
		handleProductChange(val) {
			// http://jira.evebattery.com/browse/PBIGLKF-974 录入文档：选择一级产品改为先选产品再选项目
			const product = this.allProjects.find(item => item.issueId == val)
			if (product) {
				this.projects = this.projectOptionsObj[product.productName]
				// 选择项目时，若products里面没有项目对应的issueId（产品名称是存在的），需要变更issueId
				const findIndex = this.products.findIndex(item => item.productName === product.productName)
				if (findIndex !== -1) {
					this.$set(this.products, findIndex, { ...this.products[findIndex], issueId: val })
				}
			}
			// http://jira.evebattery.com/browse/PBIGLKF-960 鉴定测试管理优化项：文档作者---有关于测试的（除了产品规范的所有文件）应该全部是测试代表的名字
			if (product && (this.form.fileTypeId === '10511'|| this.form.fileTypeId == '22713') && val) {
				this.form.accountRemark = product.productManager + "   " + product.productManagerName;
				this.fileTitleEdit()
			}
		},
		handleFileTypeIdChange(val) {
			/* if(['10514','22703'].indexOf(val) == -1){
			}
			if(['10511','10512'].indexOf(val) != -1){
			} */
			/* this.form.docType = null;
			this.form.techStatusNo = null; */

			// http://jira.evebattery.com/browse/PBIGLKF-960 鉴定测试管理优化项：文档作者---有关于测试的（除了产品规范的所有文件）应该全部是测试代表的名字
			if (val === '10511'|| val === '22713') {
				this.handleProductChange(this.form.issueId)
			} else if (this.testRepresentAccount) {
				this.form.accountRemark = this.testRepresentAccount + "   " + this.testRepresentName;
				this.fileTitleEdit()
			}
		},
		handleChangeRemark(value) {
			// 处理选项变化的逻辑
			this.form.accountRemark = value;
		},
		handleChangeCheck(value) {
			// 处理选项变化的逻辑
			this.form.accountCheck = value;
		},
		handleSearch(searchValue) {
			// 根据键盘输入调用搜索接口
			if (searchValue) { // 确保有搜索值
				const param = { searchValue: searchValue }
				getUserLists(param).then(res => {
					for (let i = 0; i < res.data.rows.length; i++) {
						res.data.rows[i].accountRemark = res.data.rows[i].account + "   " + res.data.rows[i].name
					}
					this.userList = res.data.rows ? res.data.rows : []
				})
			}
		},
		next() {
			// 校验
			if (!this.isView && !this._handleVerify(this.form)[0]) return this.$message.error(this._handleVerify(this.form)[1])
			this.current++;
			// 审核经办人自动取录入人经理
      if (!this.isView) {
        sysUserGetLeader({ account: this.userInfo.account, leaderType: '600' }).then(res => {
          if (res.success) {
            if (Array.isArray(res.data) && res.data.length > 0) {
              const first = res.data[0]
              this.form.accountCheck = first.leaderAccount + "   " + first.leaderName;
            }
          }
        })
      }
		},
		prev() {
			this.current--;
		},
		getFormattedDateTime() {
			const now = new Date();
			const year = now.getFullYear(); // 获取年份
			const month = now.getMonth() + 1; // 获取月份，月份从0开始，所以加1
			const day = now.getDate(); // 获取日期
			const hours = now.getHours(); // 获取小时
			const minutes = now.getMinutes(); // 获取分钟
			const seconds = now.getSeconds(); // 获取秒数
			// 将单个数字格式化为两位数
			const pad = (num) => num < 10 ? '0' + num : num;
			// 格式化日期时间
			const formattedDateTime = `${year}-${pad(month)}-${pad(day)} ${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
			return formattedDateTime;
		},
		getDetail() {
			let that = this
			that.isEdit = true
			getById({ id: that.id }).then(res => {
				that.form = { ...that.form, ...res.data }
				if (that.form.fileName) {
					// 假设你有一个文件对象，你可以在这里调用 addFileManually 方法
					const file = { name: that.form.fileName, id: that.form.fileId };
					that.addFileManually(file);
				}
        // 解决查阅/编辑时项目名称不显示的问题
        if (that.form.issueId) {
          const product = this.allProjects.find(item => item.issueId == that.form.issueId)
          if (product) {
            this.projects = this.projectOptionsObj[product.productName]
            // 选择项目时，若products里面没有项目对应的issueId（产品名称是存在的），需要变更issueId
            const findIndex = this.products.findIndex(item => item.productName === product.productName)
            if (findIndex !== -1) {
              this.$set(this.products, findIndex, { ...this.products[findIndex], issueId: that.form.issueId })
            }
          }
        }
        // 补充附件文件对象赋值（如实物图、测试报告PPT等）
        const url = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + that.form.additionalFileId
        this.additionalFileList = that.form.additionalFileId ? [{uid: 'additionalFile', id: that.form.additionalFileId, name: that.form.additionalFileName, status: 'done', url: url}] : []
				that.form.accountRemark = that.form.fileAuthorId + "   " + that.form.fileAuthor
				if (that.form.auditUser) {
					that.form.accountCheck = that.form.auditUser + "   " + that.form.auditUserName
				}
				that.rightForm.createUser = that.form.createName
				that.rightForm.createTime = that.form.createTime
				that.rightForm.fileStatus = that.form.fileStatus
				that.rightForm.fileStatusId = that.form.fileStatusId
				that.rightForm.createNo = that.form.createNo
				const param = { searchValue: that.form.createAccount }
				getUserLists(param).then(res => {
					if (res.data && res.data.rows && res.data.rows[0]?.sysEmpInfo) {
						that.rightForm.dept = res.data.rows[0]?.sysEmpInfo?.orgName ?? ''
					}
				})
			})
		},
		handleModuleChange(e) {
			this.typeOptions = this.typeAllOptions[e]
		},
		handleValue(e, targetObj) {
			this.form[targetObj] = e.target.value
		},
		handleFileUpload(info, targetObj) {
			const formData = new FormData()
			formData.append('file', info.file)
			sysFileInfoUpload(formData).then((res) => {
				if (res.success) {
					this.$message.success((targetObj === 'content' ? '添加' : '上传') + '成功')
					const url = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?Authorization=Bearer eyJhbGciOiJIUzUxMiJ9.**********************************************************************************************************************************************************************************************************************.P1JWgtRVk1sTPPLiCgZNuleYyPZRf2ooByC_mmu9scs6SVbpJHgSsKd8AtscjDwg3Fw7D4QN31vgtA5jeedj3g&id=' + res.data + '#toolbar=0'
					if (targetObj === 'content') {
						// 获取文本编辑器
						this.$nextTick(() => {
							const quill = this.$refs.myQuillEditor.quill
							// 获取光标位置
							const pos = quill.getSelection().index
							// 插入图片到光标位置
							quill.insertEmbed(pos, 'image', url)
						})
					} else if (targetObj == 'banner') {
						this.form.banner = url
						this.form.bannerName = info.file.name
						this.$forceUpdate()
					} else if (targetObj == 'avater') {
						this.form.avater = url
						this.$forceUpdate()
					} else if (targetObj == 'vedio') {
						this.form.vedio = url
						this.$forceUpdate()
					}
					else {
						this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + res.data
						this.fileList = [{
							uid: info.file.uid,
							id: res.data,
							name: info.file.name,
							status: 'done',
							type: info.file.type,
							url: this.pdfUrl
						}]
						this.form.fileId = res.data;
						this.form.fileName = info.file.name;
					}
				} else {
					this.$message.error((targetObj === 'content' ? '添加' : '上传') + '失败：' + res.message)
				}
			})
		},
		addFileManually(file) {
			// 手动添加文件到 fileList
			this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + file.id
			const newFile = {
				uid: -1, // 唯一标识符
				name: file.name, // 文件名
				status: 'done', // 文件状态
				response: '文件上传成功', // 服务器响应内容
				url: this.pdfUrl, // 下载链接
			};
			this.fileList = [newFile]; // 设置 fileList
		},
		handleDownloadFileRemove(file) {
			const index = this.fileList.findIndex(item => item.id == file.id)
			const newFileList = this.fileList.slice()
			newFileList.splice(index, 1)
			this.fileList = newFileList
		},
		handleUpload() {
			document.querySelector('.quill-upload .ant-btn-link').click()
		},
    getAcceptName(accept) {
      switch (accept) {
        case '.pdf':
          return 'PDF';
        case '.ppt,.pptx':
          return 'PPT';
        case 'image/*':
          return '图片';
        default:
          return '附件';
      }
    },
    beforeUpload(file, accept) {
      // 如果没有限制，直接通过
      if (!accept) return true;

      const name = file.name.toLowerCase();
      if (accept === '.pdf') {
        if (!name.endsWith('.pdf')) {
          this.$message.error('仅支持上传PDF文件');
          return false;
        }
      } else if (accept === '.ppt,.pptx') {
        if (!name.endsWith('.ppt') && !name.endsWith('.pptx')) {
          this.$message.error('仅支持上传PPT文件');
          return false;
        }
      } else if (accept === 'image/*') {
        if (!file.type.startsWith('image/')) {
          this.$message.error('仅支持上传图片文件');
          return false;
        }
      }

      return true; // 符合条件则允许上传
    },
    handleUploadChange($event) {
      if ($event.file.status === 'uploading') {
        this.additionalFileList = [$event.file]
      } else if ($event.file.status === 'done') {
        this.form.additionalFileId = $event.file.response.data.id
        this.form.additionalFileName = $event.file.name
        const url = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token') + '&id=' + this.form.additionalFileId
        this.additionalFileList = [{uid: $event.file.uid, id: this.form.additionalFileId, name: this.form.additionalFileName, status: 'done', url: url}]
      } else if ($event.file.status === 'removed') {
        this.form.additionalFileId = null
        this.form.additionalFileName = null
        this.additionalFileList = []
      } else if ($event.file.status === 'error') {
        this.$message.error(`${$event.file.name} 文件上传失败`)
      }
    },
		handleEditorChange(e) {
		},
		handleChangeDate(date, dateString, target) {

			if (dateString) {
				if (target === 'startTime') {
					this.form.startTime = dateString;
					this.fileTitleEdit()
				} else if (target === 'loseTime') {
					this.form.loseTime = dateString;
					this.firstLoadForDate = false
				}
			}


		},
		editSave() {

			if (this.form.fileTypeId == '22704') {
				this.form.fileTitle = this.tempFileTitle
			}
			// 校验
			if (!this._handleVerify(this.form)[0]) return this.$message.error(this._handleVerify(this.form)[1])
			this.form.productName = this.allProjects.find(item => item.issueId == this.form.issueId)?.productName
			this.form.projectName = this.allProjects.find(item => item.issueId == this.form.issueId)?.projectName
			this.form.productStatus = this.allProjects.find(item => item.issueId == this.form.issueId)?.productStateName
			this.form.productStatusId = this.allProjects.find(item => item.issueId == this.form.issueId)?.productState
      if (this.form.fileTypeId === '10513') {
        this.form.additionalType = this.testTypeList.find(item => item.value === this.form.additionalTypeId)?.label
        this.form.fileTitle = '[工装夹具图纸]' + this.form.projectName + '-' + this.form.additionalTypeId
      }

			//用户处理
			if (this.form.accountRemark) {
				const account_split = this.form.accountRemark.split("   ");
				// 提取 this.form.fileAuthor
				this.form.fileAuthorId = account_split[0];
				// 提取 this.form.fileAuthorId
				this.form.fileAuthor = account_split[1];
			}
			// 找到第一个空格的位置
			const account_split2 = this.form.accountCheck.split("   ");
			// 提取 this.form.fileAuthor
			this.form.auditUser = account_split2[0];
			// 提取 this.form.fileAuthorId
			this.form.auditUserName = account_split2[1];
			if (this.form.fileId) {
				this.form.testVerifyFileUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + this.form.fileId;
			}
			this.saveLoading = true
			const url = (this.$route.query.isValidationTab === 'true' ? '/validationTestRecord?activeKey=fileLibrary' : '/lab_standardManage') + `&fileType=${this.fileTypeList.findIndex(item => item.id === this.form.fileTypeId)}`
			if (this.isEdit) {
				//编辑
				edit(this.form).then(res => {
          if (!res.success) {
            this.$message.error(res.message)
            return
          }
					this.$message.success("操作成功！")
					window.close()
					window.open(url)
				}).finally(() => {
					this.saveLoading = false
				})
			} else {
				//新增
				add(this.form).then(res => {
          if (!res.success) {
            this.$message.error(res.message)
            return
          }
					this.$message.success("操作成功！")
					window.close()
					window.open(url)
				}).finally(() => {
					this.saveLoading = false
				})
			}
		},
		handleSubmit() {
			if (this.form.fileTypeId == '22704') {
				this.form.fileTitle = this.tempFileTitle
			}
			// 校验
			if (!this._handleVerify(this.form)[0]) return this.$message.error(this._handleVerify(this.form)[1])
			// 校验
			if (!this._handleVerify2(this.form)[0]) return this.$message.error(this._handleVerify2(this.form)[1])
			if (!this.form.accountCheck) {
				this.$message.error("未选择审核经办人")
				return
			}
			this.form.productName = this.allProjects.find(item => item.issueId == this.form.issueId)?.productName
			this.form.projectName = this.allProjects.find(item => item.issueId == this.form.issueId)?.projectName
			this.form.productStatus = this.allProjects.find(item => item.issueId == this.form.issueId)?.productStateName
			this.form.productStatusId = this.allProjects.find(item => item.issueId == this.form.issueId)?.productState
      if (this.form.fileTypeId === '10513') {
        this.form.additionalType = this.testTypeList.find(item => item.value === this.form.additionalTypeId)?.label
        this.form.fileTitle = '[工装夹具图纸]' + this.form.projectName + '-' + this.form.additionalTypeId
      }
			//用户处理
			if (this.form.accountRemark) {
				const account_split = this.form.accountRemark.split("   ");
				// 提取 this.form.fileAuthor
				this.form.fileAuthorId = account_split[0];
				// 提取 this.form.fileAuthorId
				this.form.fileAuthor = account_split[1];
			}
			// 找到第一个空格的位置
			const account_split2 = this.form.accountCheck.split("   ");
			// 提取 this.form.fileAuthor
			this.form.auditUser = account_split2[0];
			// 提取 this.form.fileAuthorId
			this.form.auditUserName = account_split2[1];
			if (this.form.fileId) {
				this.form.testVerifyFileUrl = process.env.VUE_APP_API_BASE_URL + '/sysFileInfo/preview?id=' + this.form.fileId;
			}
			this.btnLoading = true
			summitJIRA(this.form).then(res => {
				if (res.success) {
					this.$message.success(res.message)
					const url = (this.$route.query.isValidationTab === 'true' ? '/validationTestRecord?activeKey=fileLibrary' : '/lab_standardManage') + `&fileType=${this.fileTypeList.findIndex(item => item.id === this.form.fileTypeId)}`
					// this.$router.replace(url)
					window.close()
					window.open(url)
				} else {
					this.$message.error(res.message)
				}
				this.btnLoading = false
			});
		},
		handleCancel() {
			window.close()
		},
		callback(key) {
		},
		_handleVerify(form) {
			const required = [
				{ dataIndex: 'issueId', message: '请选择产品名称' },
				{ dataIndex: 'fileTypeId', message: '请选择所属分类' },
				{ dataIndex: 'fileVersion', message: '请选择文档版本' },
				{ dataIndex: 'accountRemark', message: '请选择文档作者' },
				{ dataIndex: 'deptId', message: '请选择文档作者部门' },
				{ dataIndex: 'startTime', message: '请选择生效日期' },
				{ dataIndex: 'loseTime', message: '请选择失效日期' },
				{ dataIndex: 'fileId', message: '请上传附件' },
				{ dataIndex: 'content', message: form.fileTypeId === '10513' ? '请输入物料描述' : '请输入内容' },
			]

      if(form.fileTypeId === '10513') {
        const findIndex = required.findIndex(item => item.dataIndex === 'fileVersion')
        if (findIndex !== -1) {
          required.splice(findIndex, 1)
        }
        required.push({ dataIndex: 'materialNo', message: '请填写物料编号' })
        required.push({ dataIndex: 'additionalTypeId', message: '请选择测试类型' })
        required.push({ dataIndex: 'additionalFileId', message: '请上传实物图' })
      }

      if (form.fileTypeId === '10515') {
        required.push({ dataIndex: 'additionalFileId', message: '请上传PPT' })
      }

			if(['22713','10513'].indexOf(form.fileTypeId) === -1) {
				required.push({ dataIndex: 'techStatusId', message: '请选择技术状态' })
			}

			if (form.fileTypeId == '10511') {
				required.push({ dataIndex: 'fileNo', message: '请输入文档编号' })
			}
			for (let i = 0; i < required.length; i++) {
				if (form[required[i].dataIndex] === 0) {
					continue
				}
				if (!form[required[i].dataIndex]) {
					return [false, required[i].message]
				}
			}
			return [true, '']
		},
		_handleVerify2(form) {
			const required = [
				{ dataIndex: 'accountCheck', message: '请选择审核经办人' },
			]
			for (let i = 0; i < required.length; i++) {
				if (form[required[i].dataIndex] === 0) {
					continue
				}
				if (!form[required[i].dataIndex]) {
					return [false, required[i].message]
				}
			}
			return [true, '']
		},

		getDictName(code, key) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
			let name = dict.find(item => item.code == key)?.name
			return name ?? '-'
		},
		fileTitleEdit() {

			console.log(this.form.fileTypeId)

      if (this.form.fileTypeId == '22704' || this.form.fileTypeId == '10513') {
				return
			}

			if (this.form.fileTypeId == '22713') {

				this.form.fileTitle = '';

				if (this.form.fileType) {
					this.form.fileTitle = this.form.fileTitle + '[' + this.form.fileType + ']';
				}

				let projectName = this.allProjects.find(item => item.issueId == this.form.issueId)?.projectName
				if (projectName) {
					this.form.fileTitle = this.form.fileTitle + projectName;
				}

				if (this.form.fileType) {
					this.form.fileTitle = this.form.fileTitle + this.form.fileType + "_";
				}

				if (this.form.fileVersion) {
					this.form.fileTitle = this.form.fileTitle + this.form.fileVersion;
					this.form.fileTitle = this.form.fileTitle + "版_"
				}

				if (this.form.startTime) {
					this.form.fileTitle = this.form.fileTitle + this.form.startTime;
				}

				return
			}

			this.form.fileTitle = '';

			if (this.form.fileType) {
				this.form.fileTitle = this.form.fileTitle + '[' + this.form.fileType + ']';
			}
			let projectName = this.allProjects.find(item => item.issueId == this.form.issueId)?.projectName
			if (projectName) {
				this.form.fileTitle = this.form.fileTitle + projectName;
			}
			this.form.fileTitle = this.form.fileTitle + " "
			if (this.form.techStatus) {
				this.form.fileTitle = this.form.fileTitle + this.form.techStatus;
			}
			if (this.form.techStatusNo !== undefined && this.form.techStatusNo !== null) {
				this.form.fileTitle = this.form.fileTitle.replace(/样$/, '') + this.form.techStatusNo + '样';
			}
			if (this.form.fileType) {
				this.form.fileTitle = this.form.fileTitle + this.form.fileType;
			}

			if (['22703', '22704'].indexOf(this.form.fileTypeId) == -1) {
				this.form.fileTitle = this.form.fileTitle + "_"
				//文档编号
				if (this.form.fileNo) {
					this.form.fileTitle = this.form.fileTitle + this.form.fileNo;
					this.form.fileTitle = this.form.fileTitle + "_"
				}

				if (this.form.fileVersion) {
					this.form.fileTitle = this.form.fileTitle + this.form.fileVersion;
					this.form.fileTitle = this.form.fileTitle + "版_"
				}

				if (this.form.startTime) {
					this.form.fileTitle = this.form.fileTitle + this.form.startTime;
				}
			}

			if (['22703'].indexOf(this.form.fileTypeId) != -1) {

				this.form.fileTitle = this.form.fileTitle + "_"

				//文档格式
				if (this.form.docType) {
					this.form.fileTitle = this.form.fileTitle + this.getDictName('doc_formate', this.form.docType);
					this.form.fileTitle = this.form.fileTitle + "_"
				}

				//文档版本
				if (this.form.fileVersion) {
					this.form.fileTitle = this.form.fileTitle + this.form.fileVersion;
					this.form.fileTitle = this.form.fileTitle + "版_"
				}

				//生效日期
				if (this.form.startTime) {
					this.form.fileTitle = this.form.fileTitle + this.form.startTime;
					this.form.fileTitle = this.form.fileTitle + "_"
				}

				if (this.form.accountRemark) {
					const account_split = this.form.accountRemark.split("   ")
					this.form.fileTitle = this.form.fileTitle + account_split[1]
				}

			}
		},
	},
	watch: {
		'form.techStatusNo': {
			handler(newVal, oldVal) {
				this.fileTitleEdit()
			},
			immediate: true,
			deep: true
		},
		'form.docType': {
			handler(newVal, oldVal) {
				this.fileTitleEdit()
			},
			immediate: true,
			deep: true
		},
		'form.issueId': {
			handler(newVal, oldVal) {
				this.fileTitleEdit()
			},
			immediate: true,
			deep: true
		},
		'form.techStatusId': {
			handler(newVal, oldVal) {
				const find = this.techStatusList.find(e => e.id == newVal)
				if (find) {
					this.form.techStatus = find.customvalue
					this.fileTitleEdit()
				}
			},
			immediate: true,
			deep: true
		},
		'form.fileTypeId': {
			handler(newVal, oldVal) {
				const find = this.fileTypeList.find(e => e.id == newVal)
				if (find) {
					this.form.fileType = find.customvalue
					this.fileTitleEdit()
				}
			},
			immediate: true,
			deep: true
		},
		'form.fileNo': {
			handler(newVal, oldVal) {
				this.fileTitleEdit()
			},
			immediate: true,
			deep: true
		},
		'form.fileVersion': {
			handler(newVal, oldVal) {
				this.fileTitleEdit()
			},
			immediate: true,
			deep: true
		},
		// 监听 form.startTime 的变化
		'form.startTime': {
			handler(newVal) {
				if (newVal) {
					if (!this.isEdit || !this.firstLoadForDate) {
						if (!this.form.loseTime) {
							const startTimeDate = new Date(newVal);
							const loseTimeDate = new Date(startTimeDate);
							loseTimeDate.setFullYear(loseTimeDate.getFullYear() + 3);
							this.form.loseTime = loseTimeDate.toISOString().split('T')[0];

						}
					} else {
						this.firstLoadForDate = false;
					}

				}

			},
			immediate: true,
			deep: true
		},
	},
}
</script>
<style lang="less" scoped>
@import '/src/style/small-ant-modal.less';
@import '~@/style/general.css';

.steps-content {
	min-height: 200px;
	text-align: center;
}

.container {
	width: calc(100vw - 80px);
	height: 100%;
	overflow-y: scroll;
	padding: 10px 100px 10px 10px;
}

.left-container {
	background: #fff;
	height: auto;
	width: 73%;
	position: absolute;
	left: 10px;
	top: 10px;
	padding: 10px 20px;
	/* 移除 height 属性 */
	overflow-y: hidden;
	/* 确保没有滚动条 */
}

.right-container {
	width: 20%;
	height: 212px;
	position: absolute;
	left: calc(74% + 40px);
	top: 10px;
	background: #fff;
}

.filling-warpper {
	flex-wrap: wrap;
}

.filling-box {
	width: fit-content;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	width: calc(100% / 3);

	input[type=text],
	input[type=password],
	input[type=email],
	input[type=number],
	input[type=tel],
	textarea {
		border: 1px solid #d9d9d9;
	}
}

.filling-box-fileTile {
	width: fit-content;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	width: 100%;
}

.label {
	margin-right: 5px;
	width: 100px;
	text-align: right;
	font-size: 14px;
}

.filling-box .label {
	margin-right: 5px;
	width: 100px;
	text-align: right;
	font-size: 14px;
}

.filling-box .plus-block {
	width: 100px;
	height: 100px;
	border: 1px solid #d9d9d9;
	border-radius: 2px;
}

.filling-box .plus-block img {
	width: 100%;
	height: 100%;
}

.tip-box-title {
	width: fit-content;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	width: 100%;
}

.tip-box-content {
	width: fit-content;
	display: flex;
	align-items: center;
	width: 100%;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 12px;
	font-family: Source Han Sans SC-Regular;
	font-weight: normal;
	white-space: nowrap;
	line-height: 16px;
	margin: 0px 0px 10px 20px;
}

.tip-label {
	width: 60px;
	text-align: left;
}

.result-wrapper {
	background: #fff;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: fixed;
	z-index: 1;
}

.step {
	width: 50%;
	position: relative;
	left: 25%;
}

.grey {
	color: rgba(191, 191, 191, 1);
	font-size: 12px;
	font-family: Source Han Sans SC-Regular;
	font-weight: normal;
}

/deep/ .ql-editor {
	min-height: 100px
}
</style>
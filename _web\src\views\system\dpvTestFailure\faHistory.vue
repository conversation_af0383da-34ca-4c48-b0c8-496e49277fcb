<template>
  <a-modal
    :title="'历史记录'"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
  >
    <template slot="footer">
      <a-button key="back" @click="handleCancel">
        关闭
      </a-button>
    </template>
    <a-spin :spinning="confirmLoading">
      <tableIndex
        :pageLevel='4'
        :pageTitleShow=false
        :loading='confirmLoading'
        style="height: 100%;border-radius: 10px;"
        @paginationChange="handlePageChange"
        @paginationSizeChange="handlePageChange"
        :tableTotal="loadData.length"
        @tableFocus="tableFocus"
        @tableBlur="tableBlur">

        <template #table>
          <ag-grid-vue :style="`height: 280px;width: 100%;`"
                       class='table ag-theme-balham'
                       :columnDefs='columns'
                       :rowData='pageData'
                       :tooltipShowDelay="0"
                       :grid-options='gridOptions'
                       :defaultColDef='defaultColDef'>
          </ag-grid-vue>
        </template>
      </tableIndex>

    </a-spin>
  </a-modal>
</template>

<script>
import Vue from 'vue'
import moment from "moment";
import {
  STable,
  clamp
} from '@/components'

export default {
  components: {
    STable,
    clamp,
  },
  data() {
    return {
      baseUrl: `http://jira.evebattery.com/rest/oa2jira/1.0/attachment/preview?auth=${Vue.ls.get("jtoken")}&attachmentId=`,
      record:{},
      datas: [],
      vcolumns: [],
      queryParam: {},
      confirmLoading: false,
      visible: false,
      loading: false,
      defaultColDef: {
        flex: 1,
        minWidth: 110,
        filter: false,
        floatingFilter: false,
        editable: false,
        tooltipValueGetter: this.pbiTooltip    // pbiTooltip  为全局组件，不需要引入
      },
      gridOptions:{
        onColumnResized: _.debounce(this.pbiRefreshCells,500),   //  pbiRefreshCells 为全局组件，不需要引入
        tooltipShowDelay: 0 , // 设置显示延迟为0毫秒
        tooltipInteraction: true,  // 允许交互
        context: { componentParent: this }, // 添加组件上下文
        onGridReady: params => {
          this.gridApi = params.api;
          this.columnApi = params.columnApi;
          // 调整列宽以适应内容
          // params.api.sizeColumnsToFit();
        },
        // suppressRowTransform: true,
        // 添加单元格点击事件处理
        onCellClicked: (event) => {
          // 检查是否点击的是文件链接列
          if (event.colDef.cellRenderer === 'fileCellRenderer') {
            this.preview(event.value);
          }
          if (event.colDef.cellRenderer === 'jiraJumpCellRenderer') {
            this.handleToJira(event.value);
          }
        },
        // register the components using 'components' grid property
        components: {
          fileCellRenderer: params => {
            console.log("params", params);
            return `<a href="javascript:void(0)" style="color: #1890ff; cursor: pointer; text-decoration: underline;">${params.value.attachmentName}</a>`;
          },
          jiraJumpCellRenderer: params => {
            console.log("params", params);
            return `<a href="javascript:void(0)" style="color: #1890ff; cursor: pointer; text-decoration: underline;">分析报告流程</a>`;
          }
        },
      },
      columns: [{
        headerName: '序号',
        field: 'index',
        minWidth: 50,
        maxWidth: 60,
        initialWidth: 50,
        width: 50,
        cellRenderer: function (params) {
          return parseInt(params.node.id) + 1
        },
      }, {
        headerName: "原因分析",
        field: "causeAnalysis",
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      }, {
        headerName: "临时措施",
        field: "tempMeasures",
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      }, {
        headerName: "长期措施",
        field: "longTermMeasures",
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      }, {
        headerName: "结果验证",
        field: "resultVerification",
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      }, {
        headerName: "拆解报告",
        field: "faBreakReport",
        cellRenderer: 'fileCellRenderer',
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
        tooltipValueGetter: (p) =>  p.value.attachmentName,
      }, {
        headerName: "分析报告",
        field: "faAnalysisReport",
        cellRenderer: 'fileCellRenderer',
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
        tooltipValueGetter: (p) =>  p.value.attachmentName,
      },{
        headerName: '填写时间',
        field: 'faSubmitDateStr',
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      },{
        headerName: '填写人',
        field: 'faSubmitterName',
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      },{
        headerName: '流程',
        field: 'faAnalyseReportIssueKey',
        cellRenderer: 'jiraJumpCellRenderer',
        minWidth: 100,
        maxWidth: 500,
        initialWidth: 150,
        width: 150,
      }],

      pageNo: 1,
      pageSize: 20,
      pageData: [],
      loadData: []
    }
  },
  created() {
  },
  methods: {
    preview(record) {//调用父组件方法预览文件
      console.log("文件预览record",record)
      this.$emit('handleFileNameClick', record.attachmentName, record.attachmentId)
    },
    handleToJira(issueKey) {
      this.$emit('handleToJira', issueKey)
    },
    tableFocus() {
      // this.$el.style.setProperty('--scroll-border-bottom-fixed', 'none');
      // this.$el.style.setProperty('--scroll-display', 'unset');
      // this.$el.style.setProperty('--scroll-border-bottom', '1px solid #Dee1e8');
    },
    // 鼠标移出
    tableBlur() {
      // this.$el.style.setProperty('--scroll-border-bottom-fixed', '1px solid #dee1e8');
      // this.$el.style.setProperty('--scroll-display', 'none');
      // this.$el.style.setProperty('--scroll-border-bottom', 'none');
    },
    handlePageChange(value) {
      let {current, pageSize} = value
      this.pageNo = current
      this.pageSize = pageSize
      //数据分页
      this.pageData = this.loadData.slice((current - 1) * pageSize, current * pageSize);
      console.log(value)
    },

    callReviewResults() {
      this.confirmLoading = true
      // let faHistory = this.record.faHistory;
      const faHistoryArray = JSON.parse(this.record.faHistory);
      faHistoryArray.sort((a, b) => b.faSubmitDate - a.faSubmitDate);
      console.log("faHistoryArray", faHistoryArray);
      console.log("this.columns", this.columns);

      for (const faHistory of faHistoryArray) {
        // faHistory.faSubmitDate
        faHistory.faSubmitDateStr = moment(faHistory.faSubmitDate).format('YYYY-MM-DD');
        this.loadData.push(faHistory);
      }
      this.pageData = this.loadData.slice((this.pageNo - 1) * this.pageSize, this.pageNo * this.pageSize);
      this.visible = true
      this.confirmLoading = false

    },
    handleCancel() {
      this.queryParam = {}
      this.datas = []
      this.loadData = []
      this.visible = false
    },
    view(record) {

      this.record = record;

      setTimeout(() => {
        this.callReviewResults()
      }, 100);
      // if (this.gridApi){
        //main.cjs.js:593 AG Grid: setColumnDefs is deprecated. Please use 'api.setGridOption('columnDefs', newValue)' or 'api.updateGridOptions({ columnDefs: newValue })' instead.
        // this.gridApi.setGridOption('columnDefs', this.columns);
      // }
      this.visible = true
    },
  },
  watch: {}
}
</script>

<style>

</style>
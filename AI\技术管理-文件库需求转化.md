# 需求说明

---

## 1、基本信息

技术管理-文件库

---

## 2、功能简述

管理【技术路线图、产品图谱、行业会议、产品介绍、其他】五个文件类别

---

## 3、业务逻辑

### 3.1、数据添加

用户点击新建，弹出弹窗填写文件相关数据

### 3.2、记录管理

添加新app技术管理，文件库新菜单。五类文件使用同一个菜单，通过tab标签切换不同表格

- 查阅：查阅使用分页查询，添加筛选功能
- 新建：新建数据，
- 删除：删除数据，管理员可删除全部，非管理员仅可删除本人新建的

### 3.3、权限控制

 通过菜单控制权限，添加 技术管理-文件库管理员、技术管理-文件库查阅角色，管理员删除他人数据需验证按钮权限

---

## 4、编码逻辑



### 4.1、后端逻辑

#### 4.1.1、编码规范

后端代码编码规范参考该文件 @后端代码规范.md

- controller：定义接口供前端调用
- service：定义业务处理方法，controller调用该层级方法实现具体业务逻辑
- mapper：定义数据访问接口，通过该层级实现对数据库的访问
- entity：定义实体类，通过该层级结构化数据
- param：定义查询类，用于接收查询参数，结构化推送第三方系统数据等
- enums：定义枚举类，用于定义改动频率较低的配置参数、映射关系，异常枚举实现AbstractBaseExceptionEnum

#### 4.1.2、数据结构

oracle数据库，表名：“TECH_MANAGE_FILE_LIBRARY”，基础类字段无需定义，继承BaseEntity基础类(@src/main/java/eve/core/pojo/base/entity/BaseEntity.java)即可

| 字段标识         | 字段名称     | 类型    | 说明                                                         | 备注       |
| ---------------- | ------------ | ------- | ------------------------------------------------------------ | ---------- |
| createTime       | 创建时间     | Date    | 创建时间                                                     | 基础类字段 |
| createAccount    | 创建人工号   | String  |                                                              | 基础类字段 |
| createName       | 创建人姓名   | String  |                                                              | 基础类字段 |
| updateTime       | 更新时间     | Date    |                                                              | 基础类字段 |
| updateAccount    | 更新人工号   | String  |                                                              | 基础类字段 |
| updateName       | 更新人姓名   | String  |                                                              | 基础类字段 |
| deleteStatus     | 逻辑删除     | Integer | 逻辑删除标识1:已删除，0:正常                                 | 基础类字段 |
| id               | 主键         | Long    |                                                              | 业务字段   |
| docTitle         | 文档标题     | String  |                                                              | 业务字段   |
| docCate          | 文件类别     | String  | 字典：10-技术路线图、20-产品图谱、30-行业会议、40-产品介绍、50-其他 | 业务字段   |
| techCate         | 技术类别     | String  | 字典：10-产品开发、20-材料平台、30-工艺技术、40-结构技术     | 业务字段   |
| department       | 研究所       | String  | jira字典，获取方法见参考                                     | 业务字段   |
| meetingSummary   | 会议主题     | String  |                                                              | 业务字段   |
| customerName     | 客户名称     | String  |                                                              | 业务字段   |
| docCode          | 文档编号     | String  |                                                              | 业务字段   |
| docVersion       | 文档版本     | String  | A-Z选择                                                      | 业务字段   |
| authorAccount    | 文档作者工号 | String  |                                                              | 业务字段   |
| authorName       | 文档作者姓名 | String  |                                                              | 业务字段   |
| authorDepartment | 文档作者部门 | String  | jira字典，获取方法见参考                                     | 业务字段   |
| effectiveDate    | 生效日期     | Date    |                                                              | 业务字段   |
| expirationDate   | 失效日期     | Date    |                                                              | 业务字段   |
| pdfFileName      | PDF文件名    | String  |                                                              | 业务字段   |
| pdfFileId        | PDF文件ID    | Long    |                                                              | 业务字段   |
| pptFileName      | PPT文件名    | String  |                                                              | 业务字段   |
| pptFileId        | PPT文件ID    | Long    |                                                              | 业务字段   |
| content          | 内容         | String  |                                                              | 业务字段   |

#### 4.1.3、业务逻辑

- 新增：新增页面包含以下字段：文档标题（实时预览，生成规则（[文件类别]业务字段_报告_标题_版本_作者_日期）），文件类别选择，【不同业务字段】，文档编号，文档版本，文档作者，文档作者部门（仅展示所），生效日期，失效日期，PDF文件，PPT文件，内容。
- 删除：表格勾选，筛选框右侧添加删除按钮，管理员可删除全部，非管理员仅可删除本人新建的数据
- 编辑：字段同新增，管理员可编辑全部，非管理员仅可编辑本人新建的数据
- 查询：page接口，需提供筛选功能，筛选字段：文档标题、文件类别

##### 技术路线图

- 新增：业务字段：类别字段（产品开发、材料平台、工艺技术、结构技术）。

##### 产品图谱

- 新增：业务字段：研究所。

##### 行业会议

- 新增：业务字段：会议主题。

##### 产品介绍

- 新增：业务字段：客户名称。

##### 其他

- 新增：无业务字段

#### 4.1.4、API接口规范

路径规范：类注解定义功能路径，方法定义操作路径

请求参数格式：

- 普通请求：请求体传入筛选数据{"字段名":"字段值"...}
- page请求：请求体传入筛选数据+分页信息{"pageSize":20,"pageNo":1,"字段名":"字段值"...}

响应数据：

请求成功使用封装，eve.core.pojo.response.SuccessResponseData，响应数据格式{"success":true,"code":200,"data":"Object"}

- 普通请求：数据位于响应的data中，可直接使用
- page请求：返回eve.core.pojo.page.PageResult格式数据，数据位于结果集data.rows

#### 4.1.5、代码存放文件夹

编写的代码存放到@src/main/java/eve/sys/modular中，新建一个文件夹techManage/fileLibrary存放

### 4.2、前端逻辑

#### 4.2.1、新增

管理页面点击新增按钮，弹出新增窗口进行新增

#### 4.2.2、查询

前端定义一个页面展示数据，使用tab切换展示五个文件类别的数据，展示形式可参考@_web/src/views/system/validationTestManage/index.vue

#### 4.2.3、代码存放文件夹：

前端代码存放到@_web/src/views/system，新建文件夹techManage/fileLibrary

包含三个页面，index首页，add新建页面，edit编辑页面

API定义到新的文件@_web/src/api/modular/system/techManage.js中

###  4.3、技术栈

- 后端：spring boot、mybatis-plus、hutool工具类
- 前端：vue-2.6.10、agGrid、

---

## 5、代码结构

5.1、后端



5.2、前端





---

## 6、参考

agGrid已封装自定义组件，位于@_web/src/components/pageTool/pageTemplate/tableInIndex.vue，使用示例位于@_web/src/components/pageTool/webAi/demo.vue，组件说明位于@，优先使用该组件

后端完整的CRUD可参考位于@src/main/java/eve/sys/modular/app的实现

后端代码编写规范可参考@后端代码规范.md

*1：LoginContextHolder结构：位于@eve.core.context.login.LoginContextHolder，me方法返回@eve.core.context.login.LoginContext，该类被@eve.sys.modular.auth.context.LoginContextSpringSecurityImpl实现

*2：导出功能参考：@GetMapping("/sysOrg/export") 方法位置：@eve.sys.modular.org.controller.SysOrgController#export

---

## 7、数据库建表语句



---




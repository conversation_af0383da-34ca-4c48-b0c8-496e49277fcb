package eve.sys.modular.test.dpvTestFailure.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.WriteContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.fasterxml.jackson.databind.ObjectMapper;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.context.login.LoginContextHolder;
import eve.core.email.MailSender;
import eve.core.email.modular.model.SendMailParam;
import eve.core.enums.CommonStatusEnum;
import eve.core.exception.ServiceException;
import eve.core.file.FileOperator;
import eve.core.pojo.email.EmailConfigs;
import eve.core.pojo.login.SysLoginUser;
import eve.core.pojo.page.PageResult;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.config.FileConfig;
import eve.sys.jiraModular.jiraUserSignature.entity.Ao8de701UserSignature;
import eve.sys.jiraModular.jiraUserSignature.service.IAo8de701UserSignatureService;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import eve.sys.limsModular.order.entity.TLimsOrder;
import eve.sys.limsModular.order.service.ITLimsOrderService;
import eve.sys.limsModular.testMatrix.entity.TLimsTestmatrix;
import eve.sys.limsModular.testMatrix.service.ITLimsTestmatrixService;
import eve.sys.modular.auth.service.AuthService;
import eve.sys.modular.dict.entity.SysDictData;
import eve.sys.modular.dict.entity.SysDictType;
import eve.sys.modular.dict.param.SysDictDataParam;
import eve.sys.modular.dict.service.SysDictDataService;
import eve.sys.modular.dict.service.SysDictTypeService;
import eve.sys.modular.file.entity.SysFileInfo;
import eve.sys.modular.file.service.SysFileInfoService;
import eve.sys.modular.file.util.DownloadUtil;
import eve.sys.modular.leader.entity.LeaderRelation;
import eve.sys.modular.minio.service.MinioService;
import eve.sys.modular.open.controller.param.TestFailureReceiveFromJIRAParam;
import eve.sys.modular.product.entity.ProductManager;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.service.IProductManagerService;
import eve.sys.modular.product.utils.Utils;
import eve.sys.modular.test.dpvTestFailure.entity.DpvTestFailureRecord;
import eve.sys.modular.test.dpvTestFailure.entity.FailureCellOcvRecord;
import eve.sys.modular.test.dpvTestFailure.mapper.DpvTestFailureRecordMapper;
import eve.sys.modular.test.dpvTestFailure.param.TsetFailureQueryParam;
import eve.sys.modular.test.dpvTestFailure.service.IDpvTestFailureRecordService;
import eve.sys.modular.test.dpvTestFailure.service.IFailureCellOcvRecordService;
import eve.sys.modular.emailSendRecord.service.IEmailSendRecordService;
import eve.sys.modular.emailSendRecord.entity.EmailSendRecord;
import eve.sys.modular.user.entity.SysUser;
import eve.sys.modular.user.service.SysUserService;
import io.minio.DownloadObjectArgs;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * DPV测试失效登记表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-10
 */
@Service
@Slf4j
public class DpvTestFailureRecordServiceImpl extends ServiceImpl<DpvTestFailureRecordMapper, DpvTestFailureRecord> implements IDpvTestFailureRecordService {

    @Resource
    private ITLimsOrderService itLimsOrderService;
    @Resource
    private ITLimsFolderService itLimsFolderService;
    @Resource
    private ITLimsTestmatrixService itLimsTestmatrixService;
    @Resource
    private ITLimsOrdtaskService itLimsOrdtaskService;
    @Resource
    private IProductManagerService iProductManagerService;
    @Resource
    private SysDictDataService dictDataService;
    @Resource
    private SysDictTypeService dictTypeService;
    @Resource
    private SysFileInfoService sysFileInfoService;
    @Resource
    private MinioService minioService;
    @Resource
    private FileOperator fileOperator;
    @Resource
    private IFailureCellOcvRecordService iFailureCellOcvRecordService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private AuthService authService;
    @Resource
    private ITLimsFolderService folderService;
    @Resource
    private MinioClient minioClient;
    @Resource
    private MailSender mailSender;

    @Resource
    private IAo8de701UserSignatureService signatureService;

    @Resource
    private IDpvTestFailureRecordService failureRecordService;

    @Resource
    private IEmailSendRecordService emailSendRecordService;

    private static final Map<String, String> headerNameTransToFieldsMap = new HashMap<>();

    private static final Map<String, String> failureCateMap = new HashMap<>();

    private static final Map<String, String> laboratoryIdMap = new HashMap<>();

    static {
        // 基本信息
        headerNameTransToFieldsMap.put("序号","no");
        headerNameTransToFieldsMap.put("产品名称","productName");
        headerNameTransToFieldsMap.put("电芯编码","cellCode");
        headerNameTransToFieldsMap.put("内阻/Ω","innerRes");
        headerNameTransToFieldsMap.put("电压/V","voltage");

        failureCateMap.put("1", "不满足指标");
        failureCateMap.put("2", "起火");
        failureCateMap.put("3", "漏液");
        failureCateMap.put("4", "壳体开裂");
        failureCateMap.put("5", "其它");

        laboratoryIdMap.put("HZ_YJ_DL_AQ", "第四实验室");
        laboratoryIdMap.put("HZ_YJ_DL_JM", "精密实验室");
        laboratoryIdMap.put("HZ_YJ_DL_CS", "第六实验室(HZ)");
    }

    private static final List<SFunction<DpvTestFailureRecord, String>> ROLE_FIELDS = Arrays.asList(
            DpvTestFailureRecord::getCreateAccount,
            DpvTestFailureRecord::getInitiatorAccount,
            DpvTestFailureRecord::getFaChargeAccount,
            DpvTestFailureRecord::getSampleManager,
            DpvTestFailureRecord::getLaboratoryResponsible,
            DpvTestFailureRecord::getDepartmentManager,
            DpvTestFailureRecord::getProductManager,
            DpvTestFailureRecord::getProductMajordomo,
            DpvTestFailureRecord::getDQE,
            DpvTestFailureRecord::getHeadOfTheInstitute,
            DpvTestFailureRecord::getVicePresident,
            DpvTestFailureRecord::getPresident
    );

    private static final EmailConfigs EMAIL_CONFIGS = ConstantContextHolder.getEmailConfigs();

    Map<String, Lock> locks = new ConcurrentHashMap<>();

    @Override
    public List<DpvTestFailureRecord> list(TsetFailureQueryParam param) {
        LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(param.getProductName())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getProductName, param.getProductName());
        }
        if (ObjectUtil.isNotEmpty(param.getStockStatus())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getStockStatus, param.getStockStatus());
        }
        if (ObjectUtil.isNotEmpty(param.getStockStatusList())) {
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getStockStatus, param.getStockStatusList());
        }
        dpvTestFailureRecordLambdaQueryWrapper.orderByDesc(DpvTestFailureRecord::getCreateTime);

        return this.list(dpvTestFailureRecordLambdaQueryWrapper);
    }

    @Override
    public List<DpvTestFailureRecord> listStock(TsetFailureQueryParam param) {
        LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getReviewStatus,"2");
        if (ObjectUtil.isNotEmpty(param.getProductName())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getProductName, param.getProductName());
        }
        dpvTestFailureRecordLambdaQueryWrapper.orderByDesc(DpvTestFailureRecord::getCreateTime);

        return this.list(dpvTestFailureRecordLambdaQueryWrapper);
    }

    @Override
    public JSONObject getSampleNumByYear(TsetFailureQueryParam param){
        String year = param.getYear();
        JSONObject jsonObject = new JSONObject();
        LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getReviewStatus,"2");
        dpvTestFailureRecordLambdaQueryWrapper.isNotNull(DpvTestFailureRecord::getSampleStatus);
        if (ObjectUtil.isNotEmpty(year) && StringUtils.isNumeric(year)){
            int yearInt = Integer.parseInt(year);
            LocalDateTime beginTime = LocalDate.of(yearInt, 1, 1).atStartOfDay();
            LocalDateTime endTime = LocalDate.of(yearInt, 12, 31).atTime(LocalTime.MAX);
            dpvTestFailureRecordLambdaQueryWrapper.ge(DpvTestFailureRecord::getInitiationTime, beginTime)
                    .le(DpvTestFailureRecord::getInitiationTime, endTime);
        }
        if (ObjectUtil.isNotEmpty(param.getSampleNumLabId())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getLaboratoryId, param.getSampleNumLabId());
        }

        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        String userAccount = sysLoginUser.getAccount();
        List<String> loginUserRoleIds = LoginContextHolder.me().getLoginUserRoleIds();
        // ”产品测试-测试失效-管理员“角色，ID:1781119467332612097，研发大数据部人员，可查看所有数据；其余只能查看与自己相关的数据
        if (!"superAdmin".equals(userAccount) && !loginUserRoleIds.contains("1781119467332612097")) {
            dpvTestFailureRecordLambdaQueryWrapper.and(wrapper -> {
                wrapper.like(DpvTestFailureRecord::getCountersigner, userAccount);
                for (SFunction<DpvTestFailureRecord, String> roleField : ROLE_FIELDS) {
                    wrapper.or().eq(roleField, userAccount);
                }
            });
        }

        List<DpvTestFailureRecord> testFailureRecordList = this.list(dpvTestFailureRecordLambdaQueryWrapper);
        jsonObject.put("sampleNum", testFailureRecordList.size());
        jsonObject.put("preInStore", testFailureRecordList.stream().filter(e-> "preInStore".equals(e.getSampleStatus())).count());
        jsonObject.put("inStore", testFailureRecordList.stream().filter(e-> "inStore".equals(e.getSampleStatus())).count());
        jsonObject.put("disassemble", testFailureRecordList.stream().filter(e-> "disassemble".equals(e.getSampleStatus())).count());
        jsonObject.put("scrap", testFailureRecordList.stream().filter(e-> "scrap".equals(e.getSampleStatus())).count());
        jsonObject.put("HZ_YJ_DL_AQ", testFailureRecordList.stream().filter(e-> "HZ_YJ_DL_AQ".equals(e.getLaboratoryId())).count());
        jsonObject.put("HZ_YJ_DL_JM", testFailureRecordList.stream().filter(e-> "HZ_YJ_DL_JM".equals(e.getLaboratoryId())).count());
        jsonObject.put("HZ_YJ_DL_CS", testFailureRecordList.stream().filter(e-> "HZ_YJ_DL_CS".equals(e.getLaboratoryId())).count());

        List<String> laboratoryIdList = Stream.concat(laboratoryIdMap.keySet().stream(), Stream.of("")).collect(Collectors.toList());
        List<String> testTypeList = Arrays.asList("研发测试", "产品验证测试", "产品鉴定测试", "应用边界测试");
        List<String> sampleStatusList = Arrays.asList("all", "preInStore", "inStore", "disassemble", "scrap");
        JSONObject labSampleNumObj = new JSONObject();
        for (String laboratoryId : laboratoryIdList) {
            List<JSONObject> sampleNumObjList = new ArrayList<>();
            for (String testType : testTypeList) {
                JSONObject sampleNumObj = new JSONObject();
                for (String sampleStatus : sampleStatusList) {
                    long count = testFailureRecordList.stream().filter(item ->
                            ("".equals(laboratoryId) || laboratoryId.equals(item.getLaboratoryId()))
                                    && testType.equals(item.getTestType())
                                    && ("all".equals(sampleStatus) || sampleStatus.equals(item.getSampleStatus()))
                    ).count();
                    sampleNumObj.put(sampleStatus, count);
                }
                sampleNumObjList.add(sampleNumObj);
            }
            labSampleNumObj.put(laboratoryId, sampleNumObjList);
        }
        jsonObject.put("labSampleNumObj", labSampleNumObj);

        return jsonObject;
    }

    @Override
    public DpvTestFailureRecord previewFailureNotice(DpvTestFailureRecord param) {
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        if (StrUtil.isEmpty(param.getFileCode())) {
            String productName = param.getProductName();
            String researchStage = param.getResearchStage();
            String fileCode = "PBI_FN_" + productName + "_" + researchStage.charAt(0);
            LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFileCode, fileCode);
            List<DpvTestFailureRecord> list = this.list(dpvTestFailureRecordLambdaQueryWrapper);
            Optional<Long> max = list.stream().map(e -> {
                String[] split = e.getFileCode().split("_");
                return Long.valueOf(split[split.length - 1]);
            }).max(Comparator.comparingLong(Long::longValue));
            if (max.isPresent()) {
                String s = max.get() + 1 + "";
                while (s.length() < 3) {
                    s = "0" + s;
                }
                fileCode += "_" + s;
            } else {
                fileCode += "_001";
            }
            param.setFileCode(fileCode);
        }
        param.setInitiatorAccount(sysLoginUser.getAccount());
        param.setInitiatorName(sysLoginUser.getName());
        param.setInitiationTime(new Date());
        // 根据模版和登记信息生成测试失效告知书，更新fileName、fileId字段
        setFailureNoticeFile(param, false, null, null);
        return param;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateDpvTestFailure(DpvTestFailureRecord param, String token) {
        Long parentId = param.getId();
        if (parentId == null) {
            return ResponseData.error("ID为空，请检查！");
        }
        // fileId预览时生成，fileCode会传过来，fileName同步
        if (StrUtil.isNotEmpty(param.getFileCode())) {
            param.setFileName(param.getFileCode() + ".pdf");
        }
        List<TLimsOrder> orderList = param.getOrderList();//根据新的电芯重新生成数据


        LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DpvTestFailureRecord::getId, DpvTestFailureRecord::getParentId, DpvTestFailureRecord::getDpvTestFailureRecordIssueKey, DpvTestFailureRecord::getOrderNo, DpvTestFailureRecord::getCellCode);
        queryWrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId);
        queryWrapper.orderByAsc(DpvTestFailureRecord::getParentId);
        List<DpvTestFailureRecord> records = this.list(queryWrapper);

        List<DpvTestFailureRecord> updateRecordList = new ArrayList<>();
        List<DpvTestFailureRecord> addRecordList = new ArrayList<>();
        List<DpvTestFailureRecord> deleteRecordList = new ArrayList<>();
        for (int i = 0; i < orderList.size(); i++) {
            TLimsOrder limsOrder = orderList.get(i);
            if (records.size() > i) {//更新
                DpvTestFailureRecord record = records.get(i);
                DpvTestFailureRecord build = BeanUtil.copyProperties(param, DpvTestFailureRecord.class);
                build.setId(record.getId());
                build.setParentId(record.getParentId());
                build.setOrderId(limsOrder.getId());
                build.setOrderNo(limsOrder.getOrderno());
                build.setCellCode(StrUtil.isEmpty(limsOrder.getCelltestcode()) ? limsOrder.getOrderno() : limsOrder.getCelltestcode());
                updateRecordList.add(build);
            }else{//原来的样品数比新样品数少，新增数据
                DpvTestFailureRecord build = BeanUtil.copyProperties(param, DpvTestFailureRecord.class);
                build.setParentId(parentId);
                build.setOrderId(limsOrder.getId());
                build.setOrderNo(limsOrder.getOrderno());
                build.setCellCode(StrUtil.isEmpty(limsOrder.getCelltestcode()) ? limsOrder.getOrderno() : limsOrder.getCelltestcode());
                addRecordList.add(build);
            }
        }
        if (records.size() > orderList.size()) {//有多余的样品，需删除
            deleteRecordList.addAll(records.subList(orderList.size() - 1, records.size() - 1));
        }


/*        for (DpvTestFailureRecord record : records) {
            DpvTestFailureRecord build = BeanUtil.copyProperties(param, DpvTestFailureRecord.class);
            build.setId(record.getId());
            build.setParentId(null);
            build.setOrderNo(record.getOrderNo());
            build.setCellCode(record.getCellCode());
            updateRecordList.add(build);
        }*/

        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();

        if (Objects.equals(param.getDpvTestFailureRecordIssueKey(), "submit")) {
            // 传递IssueKey为submit标识时提交暂存的记录，需新增JIRA流程
            Map<String, Object> jiraParam = new HashMap<>();
            jiraParam.put("userName", sysLoginUser.getAccount());
            List<SysDictData> dictDataList = this.getDictByCode("JIRA_IssueTypeId");
            Optional<SysDictData> optional = dictDataList.stream().filter(item -> "DPVTestFailureRecord".equals(item.getCode())).findFirst();
            optional.ifPresent(dictData -> jiraParam.put("issueType", dictData.getValue()));
            Map<String, Object> map = BeanUtil.beanToMap(param);
            map.put("businessId", parentId);
            map.put("cellNum", orderList.size());
            map.put("failureCate", failureCateMap.getOrDefault(param.getFailureCate(), ""));
            map.put("laboratoryId", laboratoryIdMap.getOrDefault(param.getLaboratoryId(), ""));
            map.put("cellCode", orderList.stream().map(item -> StrUtil.isEmpty(item.getCellcode()) || "NA".equals(item.getCellcode()) ? item.getOrderno() : item.getCellcode()).collect(Collectors.joining("\r\n")));
            map.put("summary", param.getSummary()+"-失效登记");
            map.remove("orderList");
            map.remove("orderIdList");
            jiraParam.put("map", map);

            log.info("测试失效登记提交审核信息" + JSON.toJSONString(jiraParam));
            JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
            log.info("测试失效登记提交审核返回" + JSON.toJSONString(jiraResp));
            if (jiraResp.getBoolean("result")) {
                updateRecordList.forEach(item -> {
                    item.setInitiatorAccount(sysLoginUser.getAccount());
                    item.setInitiatorName(sysLoginUser.getName());
                    item.setInitiationTime(new Date());
                    item.setReviewStatus("1");
                    if (!"否".equals(param.getNeedInStore())) {
                        item.setSampleStatus("preInStore");
                        item.setStockStatus("preInStore");
                    }
                    item.setDpvTestFailureRecordIssueKey(jiraResp.getString("value"));
                    item.setFaResponsibleAssignIssueKey(jiraResp.getString("value"));
                });
                addRecordList.forEach(item -> {
                    item.setInitiatorAccount(sysLoginUser.getAccount());
                    item.setInitiatorName(sysLoginUser.getName());
                    item.setInitiationTime(new Date());
                    item.setReviewStatus("1");
                    if (!"否".equals(param.getNeedInStore())) {
                        item.setSampleStatus("preInStore");
                        item.setStockStatus("preInStore");
                    }
                    item.setDpvTestFailureRecordIssueKey(jiraResp.getString("value"));
                    item.setFaResponsibleAssignIssueKey(jiraResp.getString("value"));
                });
            } else {
                throw new ServiceException(500, "测试失效登记提交JIRA审核失败：" + jiraResp.getString("message"));
            }

        } else if (StrUtil.isNotEmpty(param.getDpvTestFailureRecordIssueKey())) {
            // 失效登记IssueKey不为空说明为JIRA驳回登记，需重新推送更新信息到JIRA
            Map<String, Object> map = BeanUtil.beanToMap(param);
            map.put("businessId", parentId);
            map.put("summary", param.getSummary()+"-失效登记");
            map.put("failureCate", failureCateMap.getOrDefault(param.getFailureCate(), ""));
            map.put("laboratoryId", laboratoryIdMap.getOrDefault(param.getLaboratoryId(), ""));
            map.put("cellCode", orderList.stream().map(item -> StrUtil.isEmpty(item.getCellcode()) || "NA".equals(item.getCellcode()) ? item.getOrderno() : item.getCellcode()).collect(Collectors.joining("\r\n")));
//            map.remove("cellCode");
            map.remove("orderList");
            map.remove("orderIdList");

            Map<String, Object> attachmentObject = new HashMap<>();
            List<JSONObject> picObjList = JSON.parseObject(param.getFailureDescriptionPicture(), List.class);
            JSONArray picJsonArray = new JSONArray();
            for (JSONObject picObj : picObjList) {
                JSONObject buildObj = new JSONObject();
                buildObj.put("attachmentId", picObj.get("id"));
                buildObj.put("attachmentName", picObj.get("name"));
                picJsonArray.add(buildObj);
            }
            attachmentObject.put("failureDescriptionPicture", picJsonArray);
            JSONObject noticeFileObj = new JSONObject();
            noticeFileObj.put("attachmentId", param.getFileId());
            noticeFileObj.put("attachmentName", param.getFileCode() + ".pdf");
            attachmentObject.put("noticeOfFailure", Collections.singletonList(noticeFileObj));
            map.put("attachmentObject", JSON.toJSONString(attachmentObject));

            Map<String, Object> params = new HashMap<String, Object>(4) {
                {
                    put("issueKey", param.getDpvTestFailureRecordIssueKey());
                    put("userName", sysLoginUser.getAccount());
                    put("transitionId", "31");
                    put("map", map);
                }
            };
            log.info("修改测试失效登记提交JIRA审核信息" + JSON.toJSONString(params));
            JSONObject resp = Utils.doPostAndToken(JiraApiParams.runJiraTransitionById, token, params);
            log.info("修改测试失效登记提交JIRA审核返回" + JSON.toJSONString(resp));
            if (resp.getBoolean("result")) {
                updateRecordList.forEach(record -> {
                    record.setReviewStatus("1");
                    if (!"否".equals(param.getNeedInStore())) {
                        record.setSampleStatus("preInStore");
                        record.setStockStatus("preInStore");
                    }
                });
                addRecordList.forEach(record -> {
                    record.setReviewStatus("1");
                    if (!"否".equals(param.getNeedInStore())) {
                        record.setSampleStatus("preInStore");
                        record.setStockStatus("preInStore");
                    }
                });
            } else {
                throw new ServiceException(500, "修改测试失效登记提交JIRA审核失败：" + resp.getString("message"));
            }
        }
        if (!deleteRecordList.isEmpty()) {
            this.removeByIds(deleteRecordList);
        }
        if (!addRecordList.isEmpty()) {
            this.saveBatch(addRecordList);
        }

        return new SuccessResponseData(this.updateBatchById(updateRecordList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData faAnalysisFieldSubmit(DpvTestFailureRecord param, String token) {
        final String faAnalyseReportIssueKey = param.getFaAnalyseReportIssueKey();
        if (StrUtil.isEmpty(faAnalyseReportIssueKey)) {
            return ResponseData.error("FA分析报告IssueKey为空，请检查！");
        }

        LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DpvTestFailureRecord::getId, DpvTestFailureRecord::getFaStatus, DpvTestFailureRecord::getFaAnalysisReportId, DpvTestFailureRecord::getFaBreakReportId, DpvTestFailureRecord::getAllFaBreakReport, DpvTestFailureRecord::getAllFaBreakReport, DpvTestFailureRecord::getProductName, DpvTestFailureRecord::getTestSampleStage, DpvTestFailureRecord::getFaHistory);
        queryWrapper.eq(DpvTestFailureRecord::getFaAnalyseReportIssueKey, faAnalyseReportIssueKey);
        List<DpvTestFailureRecord> faAnalysisRecords = this.list(queryWrapper);
        if (CollectionUtil.isEmpty(faAnalysisRecords)) {
            return ResponseData.error("找不到当前FA分析报告IssueKey记录，请检查！");
        }

        List<DpvTestFailureRecord> updateRecordList = new ArrayList<>();
        for (DpvTestFailureRecord record : faAnalysisRecords) {
            DpvTestFailureRecord build = BeanUtil.copyProperties(param, DpvTestFailureRecord.class);
            build.setId(record.getId());
            build.setParentId(null);
            updateRecordList.add(build);
        }

        String faAnalysisReportId = param.getFaAnalysisReportId();
        String faAnalysisReportName = param.getFaAnalysisReportName();
        String faBreakReportId = param.getFaBreakReportId();
        String faBreakReportName = param.getFaBreakReportName();

        // 拆解报告和分析报告规范命名
        // 《拆解报告》命名规则：产品名称-样品阶段-FA拆解报告-A版-20240430
        // 《分析报告》命名规则：产品名称-样品阶段-FA分析报告-A版-20240430
        DpvTestFailureRecord faRecord = faAnalysisRecords.get(0);
        String allFaBreakReport = faRecord.getAllFaBreakReport();
        List<JSONObject> breakList = StrUtil.isNotEmpty(allFaBreakReport)
                ? JSONObject.parseArray(allFaBreakReport, JSONObject.class)
                : new ArrayList<>();
        String allFaAnalysisReport = faRecord.getAllFaAnalysisReport();
        List<JSONObject> analysisList = StrUtil.isNotEmpty(allFaAnalysisReport)
                ? JSONObject.parseArray(allFaAnalysisReport, JSONObject.class)
                : new ArrayList<>();
        //fa历史记录 驳回根据issueKey替换历史记录 提交添加历史记录
        String faHistory = faRecord.getFaHistory();
        List<JSONObject> faHistoryList = StrUtil.isNotEmpty(faHistory)
                ? JSONObject.parseArray(faHistory, JSONObject.class)
                : new ArrayList<>();
        // 如果是驳回修改，找到文件列表对应的文件索引并替换文件，而不是新增文件
        int breakIndex = -1;
        int analysisIndex = -1;
        int faHistoryIndex = -1;
        if ("reviewRejected".equals(faRecord.getFaStatus())) {
            String oldBreakId = faRecord.getFaBreakReportId();
            for (int i = 0; i < breakList.size(); i++) {
                if (Objects.equals(oldBreakId, breakList.get(i).getString("fileId"))) {
                    breakIndex = i;
                    break;
                }
            }
            String oldAnalysisId = faRecord.getFaAnalysisReportId();
            for (int i = 0; i < analysisList.size(); i++) {
                if (Objects.equals(oldAnalysisId, analysisList.get(i).getString("fileId"))) {
                    analysisIndex = i;
                    break;
                }
            }
            for (int i = 0; i < faHistoryList.size(); i++) {
                if (Objects.equals(faAnalyseReportIssueKey, faHistoryList.get(i).getString("faAnalyseReportIssueKey"))) {
                    faHistoryIndex = i;
                    break;
                }
            }
        }
        // 根据历史文件数量确定顺序编号
        String breakLetterCode = numberToLetterCode(breakIndex == -1 ? breakList.size() : breakIndex);
        String analysisLetterCode = numberToLetterCode(analysisIndex == -1 ?  analysisList.size() : analysisIndex);
        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        // 拼接文件名
        if (StrUtil.isNotEmpty(faBreakReportName) && faBreakReportName.contains(".")) {
            String breakSuffix = faBreakReportName.split("\\.")[faBreakReportName.split("\\.").length - 1];
            faBreakReportName = faRecord.getProductName() + "-" + faRecord.getTestSampleStage() + "-FA拆解报告-" + breakLetterCode + "版-" + dateStr + "." + breakSuffix;
        }
        if (StrUtil.isNotEmpty(faAnalysisReportName) && faAnalysisReportName.contains(".")) {
            String analysisSuffix = faAnalysisReportName.split("\\.")[faAnalysisReportName.split("\\.").length - 1];
            faAnalysisReportName = faRecord.getProductName() + "-" + faRecord.getTestSampleStage() + "-FA分析报告-" + analysisLetterCode + "版-" + dateStr + "." + analysisSuffix;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("causeAnalysis", param.getCauseAnalysis());
        map.put("tempMeasures", param.getTempMeasures());
        map.put("longTermMeasures", param.getLongTermMeasures());
        map.put("resultVerification", param.getResultVerification());
        Map<String, Object> attachmentObject = new HashMap<>();
        JSONObject faAnalysisReportObj = new JSONObject();
        faAnalysisReportObj.put("attachmentId", faAnalysisReportId);
        faAnalysisReportObj.put("attachmentName", faAnalysisReportName);
        attachmentObject.put("faAnalysisReport", Collections.singletonList(faAnalysisReportObj));
        JSONObject faBreakReportObj = new JSONObject();
        faBreakReportObj.put("attachmentId", faBreakReportId);
        faBreakReportObj.put("attachmentName", faBreakReportName);
        attachmentObject.put("faBreakReport", Collections.singletonList(faBreakReportObj));
        map.put("attachmentObject", JSON.toJSONString(attachmentObject));
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        Map<String, Object> params = new HashMap<String, Object>(4) {
            {
                put("issueKey", faAnalyseReportIssueKey);
                put("userName", sysLoginUser.getAccount());
                put("transitionId", "131");
                put("map", map);
            }
        };

        log.info("提交FA分析内容到JIRA审核信息" + JSON.toJSONString(params));
//        JSONObject resp = Utils.doPostAndToken(JiraApiParams.runJiraTransitionById, token, params);
//        log.info("提交FA分析内容到JIRA审核返回" + JSON.toJSONString(resp));

//        if (!resp.getBoolean("result")) {
//            throw new ServiceException(500, "提交FA分析内容到JIRA审核失败：" + resp.getString("message"));
//        }

        // 增加拆解报告和分析报告
        JSONObject breakReportObj = new JSONObject();
        breakReportObj.put("fileId", faBreakReportId);
        breakReportObj.put("fileName", faBreakReportName);
        if (breakIndex == -1) {
            breakList.add(breakReportObj);
        } else {
            breakList.set(breakIndex, breakReportObj);
        }
        JSONObject analysisReportObj = new JSONObject();
        analysisReportObj.put("fileId", faAnalysisReportId);
        analysisReportObj.put("fileName", faAnalysisReportName);
        if (analysisIndex == -1) {
            analysisList.add(analysisReportObj);
        } else {
            analysisList.set(analysisIndex, faAnalysisReportObj);
        }
        //fa历史记录
        Date date = new Date();
        JSONObject faHistoryObj = new JSONObject();
        faHistoryObj.put("faSubmitterAccount", sysLoginUser.getAccount());
        faHistoryObj.put("faSubmitterName", sysLoginUser.getName());
        faHistoryObj.put("faSubmitDate", date);
        faHistoryObj.put("causeAnalysis", param.getCauseAnalysis());
        faHistoryObj.put("tempMeasures", param.getTempMeasures());
        faHistoryObj.put("longTermMeasures", param.getLongTermMeasures());
        faHistoryObj.put("resultVerification", param.getResultVerification());
        faHistoryObj.put("faBreakReport", faAnalysisReportObj);
        faHistoryObj.put("faAnalysisReport", faBreakReportObj);
        faHistoryObj.put("faAnalyseReportIssueKey", faAnalyseReportIssueKey);
        if (faHistoryIndex == -1) {
            faHistoryList.add(faHistoryObj);
        } else {
            faHistoryList.set(faHistoryIndex, faHistoryObj);
        }
        // 修改全部报告字段
        Long parentId = Objects.equals(param.getParentId(), 1L) ? param.getId() : param.getParentId();
        LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId);
        updateWrapper.set(DpvTestFailureRecord::getAllFaBreakReport, JSONObject.toJSONString(breakList));
        updateWrapper.set(DpvTestFailureRecord::getAllFaAnalysisReport, JSONObject.toJSONString(analysisList));
        updateWrapper.set(DpvTestFailureRecord::getFaHistory, JSONObject.toJSONString(faHistoryList));
//        this.update(updateWrapper);

        for (DpvTestFailureRecord record : updateRecordList) {
            record.setFaStatus("inReview");
            // 使用更新后的文件名
            record.setFaBreakReportName(faBreakReportName);
            record.setFaAnalysisReportName(faAnalysisReportName);
        }
        return new SuccessResponseData();
//        return new SuccessResponseData(this.updateBatchById(updateRecordList));
    }

    @Override
    public void delete(DpvTestFailureRecord param) {
        LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(qw -> qw.in(DpvTestFailureRecord::getId, param.getIdList()).or().in(DpvTestFailureRecord::getParentId, param.getIdList()));
        List<DpvTestFailureRecord> list = this.list(queryWrapper);

        this.removeByIds(list.stream().map(DpvTestFailureRecord::getId).collect(Collectors.toList()));
    }

    /**
     * 将数字索引转换为字母编号，用于生成 A, B, ..., Z, AA, AB, ... 字母编号
     * 示例：0 -> A,1 -> B,25 -> Z,26 -> AA,27 -> AB
     *
     * @param index 0-based 索引
     * @return 对应的字母编号
     */
    private static String numberToLetterCode(int index) {
        if (index < 0 || index > 600) {
            return String.valueOf(index);
        }

        if (index < 26) {
            return String.valueOf((char) ('A' + index));
        } else {
            int first = index / 26 - 1;  // 第一位字母（0=A, 1=B...）
            int second = index % 26;     // 第二位字母
            return "" + (char) ('A' + first) + (char) ('A' + second);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDpvTestFailure(DpvTestFailureRecord param) {

        // 加锁
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        Lock lock = locks.computeIfAbsent(sysLoginUser.getAccount(), k -> new ReentrantLock());
        lock.lock();
        try {

        // 判断传参是否包含已登记的失效电芯
        List<TLimsOrder> orderList = param.getOrderList();
        if (CollectionUtil.isNotEmpty(orderList)) {
            List<Long> orderIdList = orderList.stream().map(TLimsOrder::getId).collect(Collectors.toList());
            LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(DpvTestFailureRecord::getOrderId, DpvTestFailureRecord::getCellCode);
            queryWrapper.in(DpvTestFailureRecord::getOrderId, orderIdList);
            List<DpvTestFailureRecord> recordList = this.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(recordList)) {
                String cellCodeListStr = recordList.stream().map(DpvTestFailureRecord::getCellCode).collect(Collectors.joining(","));
                throw new ServiceException(500, "提交失败，电芯编码中存在已登记的失效电芯：" + cellCodeListStr + "，请检查！");
            }
        }

        // 暂存先不生成相关字段
        if (!Objects.equals(param.getReviewStatus(), "-10")) {
            if (StrUtil.isEmpty(param.getFileCode())) {
                String productName = param.getProductName();
                String researchStage = param.getResearchStage();
                String fileCode = "PBI_FN_" + productName + "_" + researchStage.charAt(0);
                LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
                dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFileCode, fileCode);
                List<DpvTestFailureRecord> list = this.list(dpvTestFailureRecordLambdaQueryWrapper);
                Optional<Long> max = list.stream().map(e -> {
                    String[] split = e.getFileCode().split("_");
                    return Long.valueOf(split[split.length - 1]);
                }).max(Comparator.comparingLong(Long::longValue));
                if (max.isPresent()) {
                    String s = max.get() + 1 + "";
                    while (s.length() < 3) {
                        s = "0" + s;
                    }
                    fileCode += "_" + s;
                } else {
                    fileCode += "_001";
                }
                param.setFileCode(fileCode);
            }
            param.setFileName(param.getFileCode() + ".pdf");
            param.setInitiationTime(new Date());
        }

        param.setInitiatorAccount(sysLoginUser.getAccount());
        param.setInitiatorName(sysLoginUser.getName());
        //拆分成多条数据保存
        param.setParentId(1L);
        List<DpvTestFailureRecord> saveList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(orderList)) {
            for (int i = 0; i < orderList.size(); i++) {
                TLimsOrder limsOrder = orderList.get(i);
//                String cellCode = param.getCellCodeList().get(i);
                DpvTestFailureRecord dpvTestFailureRecord = new DpvTestFailureRecord();
                BeanUtil.copyProperties(param, dpvTestFailureRecord);
                dpvTestFailureRecord.setOrderId(limsOrder.getId());
                dpvTestFailureRecord.setCellCode(StrUtil.isEmpty(limsOrder.getCelltestcode()) ?limsOrder.getOrderno():limsOrder.getCelltestcode());
                dpvTestFailureRecord.setOrderNo(limsOrder.getOrderno());
                this.save(dpvTestFailureRecord);
                if (param.getParentId() == 1L) {
                    param.setParentId(dpvTestFailureRecord.getId());
                }
                saveList.add(dpvTestFailureRecord);
            }
        }

        // 传递reviewStatus=-10标识暂存数据，暂不提交JIRA
            if (Objects.equals(param.getReviewStatus(), "-10")) {
                return;
            }

        DpvTestFailureRecord dpvTestFailureRecord = saveList.stream().filter(e -> e.getParentId() == 1L).findFirst().orElse(new DpvTestFailureRecord());
//        boolean save = this.save(param);

            // 根据模版和登记信息生成测试失效告知书，更新fileName、fileId字段
//            setFailureNoticeFile(param);

        //信息推送JIRA生成流程审核
//        if (save) {
        HashMap<String, Object> jiraParam = new HashMap<>();
        jiraParam.put("userName", sysLoginUser.getAccount());

        List<SysDictData> sysDictDataList = this.getDictByCode("JIRA_IssueTypeId");
        Optional<SysDictData> first = sysDictDataList.stream().filter(e -> "DPVTestFailureRecord".equals(e.getCode())).findFirst();
        first.ifPresent(sysDictData -> jiraParam.put("issueType", sysDictData.getValue()));
        Map<String, Object> map = BeanUtil.beanToMap(param);
        map.put("businessId", dpvTestFailureRecord.getId());
        map.put("cellNum", saveList.size());
        map.put("failureCate", failureCateMap.getOrDefault(param.getFailureCate(), ""));
        map.put("laboratoryId", laboratoryIdMap.getOrDefault(param.getLaboratoryId(), ""));
        map.put("cellCode", orderList.stream().map(item -> StrUtil.isEmpty(item.getCelltestcode()) ? item.getOrderno() : item.getCelltestcode()).collect(Collectors.joining("\r\n")));
        map.put("summary", dpvTestFailureRecord.getSummary()+"-失效登记");
        map.remove("orderList");
        map.remove("orderIdList");
        jiraParam.put("map", map);
//        jiraParam.put("orderList","");
//        jiraParam.put("orderIdList","");

        log.info("提交审核信息" + JSON.toJSONString(jiraParam));
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
        log.info("提交审核返回" + JSON.toJSONString(jiraResp));

        //提交成功
        if (jiraResp.getBoolean("result")) {

            saveList.forEach(e -> {
                e.setReviewStatus("1");
                if (!"否".equals(param.getNeedInStore())) {
                    e.setSampleStatus("preInStore");
                    e.setStockStatus("preInStore");
                }
                e.setDpvTestFailureRecordIssueKey(jiraResp.getString("value"));
                e.setFaResponsibleAssignIssueKey(jiraResp.getString("value"));
            });
//            param.setReviewStatus("1");
//            param.setDpvTestFailureRecordIssueKey(jiraResp.getString("value"));
        } else {
            saveList.forEach(e -> {
                param.setReviewStatus("0");
            });
//            param.setReviewStatus("0");
            throw new ServiceException(500, "提交审批失败。" + jiraResp.getString("message"));
        }
        this.updateBatchById(saveList);
//        }

        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据模版和登记信息生成测试失效告知书，更新fileName、fileId字段
     * @param param 测试失效登记对象
     * @param needApprove 是否需要加审批签名
     * @param reviewer 审核人
     * @param approver 批准人
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setFailureNoticeFile(DpvTestFailureRecord param, boolean needApprove, String reviewer, String approver) {

        Map<String, Object> fillDataMap = new HashMap<>();
        fillDataMap.put("fileCode", param.getFileCode());
        fillDataMap.put("productName", param.getProductName());
        fillDataMap.put("cellBatch", param.getCellBatch());
        fillDataMap.put("projectName", param.getProjectName());
        fillDataMap.put("projectLevel", param.getProjectLevel() + "级");
        fillDataMap.put("researchStage", param.getResearchStage());
        fillDataMap.put("testSampleStage", param.getTestSampleStage());
        fillDataMap.put("testProjectName", param.getTestProjectName());
        fillDataMap.put("failureDate", DateUtil.format(param.getFailureDate(), "yyyy-MM-dd"));
        fillDataMap.put("initiatorName", param.getInitiatorName());
        fillDataMap.put("initiationTime", DateUtil.format(param.getInitiationTime(), "yyyy-MM-dd"));
        fillDataMap.put("testType", param.getTestType());
        fillDataMap.put("testCate", param.getTestCate());
        fillDataMap.put("failureCate", param.getFailureCate());
        fillDataMap.put("otherFailureCate", param.getOtherFailureCate());
        fillDataMap.put("testFailureDescription", param.getTestFailureDescription());

        // 测试失效描述图片解析
        String pictureListJson = param.getFailureDescriptionPicture();
        List<PictureRenderData> pictureRenderList = getPictureRenderList(pictureListJson);
        fillDataMap.put("descPicList", pictureRenderList);

        // 获取电子签名，没有则录入文字（若JIRA传参为空尝试从数据库获取审核人和批准人）
        if (needApprove) {
            if (StrUtil.isEmpty(reviewer)) {
                reviewer = param.getDepartmentManager();
            }
            if (StrUtil.isNotEmpty(reviewer)) {
                Ao8de701UserSignature userSignature = signatureService.querySignByNum(reviewer);
                PictureRenderData pictureRender = getPictureRender(userSignature);
                if (pictureRender != null) {
                    fillDataMap.put("reviewerPic", pictureRender);
                } else {
                    SysUser reviewerUser = sysUserService.getUserByCount(reviewer);
                    if (reviewerUser != null) {
                        fillDataMap.put("reviewerName", reviewerUser.getName());
                    }
                }
            }

            if (StrUtil.isEmpty(approver)) {
                approver = "产品鉴定测试".equals(param.getTestType()) ? param.getVicePresident() : param.getFolderUserAccount();
            }
            if (StrUtil.isNotEmpty(approver)) {
                Ao8de701UserSignature userSignature = signatureService.querySignByNum(approver);
                PictureRenderData pictureRender = getPictureRender(userSignature);
                if (pictureRender != null) {
                    fillDataMap.put("approverPic", pictureRender);
                } else {
                    SysUser approverUser = sysUserService.getUserByCount(approver);
                    if (approverUser != null) {
                        fillDataMap.put("approverName", approverUser.getName());
                    }
                }
            }
        }

        XWPFTemplate xwpfTemplate = null;
        try (
                FileInputStream fileInputStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.TEST_FAILURE_NOTICE_FILE, "测试失效告知书.docx"))
        ) {

            if (fileInputStream.available() == 0) {
                throw new IllegalArgumentException("测试失效告知书模版文件读取错误！");
            }

            // 根据模板生成Word
            // 配置 标签改为 ${var}
            ConfigureBuilder builder = Configure.builder();
            builder.buildGramer("${", "}");
            builder.useSpringEL(false);
            // 创建XWPFTemplate对象，读取模板文件并渲染数据
            xwpfTemplate = XWPFTemplate.compile(fileInputStream, builder.build()).render(fillDataMap);

            // 将渲染结果写入内存流
            ByteArrayOutputStream wordBufStream = new ByteArrayOutputStream();
            xwpfTemplate.write(wordBufStream);

            // 使用 Aspose.Words 将 Word 流转换为 PDF 流
            Document document = new Document(new ByteArrayInputStream(wordBufStream.toByteArray()));
            ByteArrayOutputStream pdfBufStream = new ByteArrayOutputStream();
            document.save(pdfBufStream, SaveFormat.PDF);

            // 4. 用 Aspose.PDF 加载并检查PDF最后一页
            com.aspose.pdf.Document pdfDoc = new com.aspose.pdf.Document(new ByteArrayInputStream(pdfBufStream.toByteArray()));
            int pageCount = pdfDoc.getPages().size();
            if (pageCount > 0) {
                com.aspose.pdf.Page lastPage = pdfDoc.getPages().get_Item(pageCount);
                // 判断最后一页是否空白并删除
                if (lastPage.isBlank(0.01)) {
                    pdfDoc.getPages().delete(pageCount);
                }
            }
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            pdfDoc.save(pdfOutputStream);
            pdfDoc.close();

            // 上传测试失效告知书到服务器
            String fileName = param.getFileCode() + ".pdf";
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, null, new ByteArrayInputStream(pdfOutputStream.toByteArray()));
            Long fileId = minioService.uploadFile(null, multipartFile, null, null);

            // 更新fileName,fileId字段
            param.setFileName(fileName);
            param.setFileId(fileId + "");

        } catch (Exception e) {
            log.error(">>> 生成测试失效告知书异常，id为：{}，fileCode为：{}，具体信息为：{}", param.getId(), param.getFileCode(), e.getMessage());
        } finally {
            if (xwpfTemplate != null) {
                try {
                    xwpfTemplate.close();
                } catch (IOException ignored) {

                }
            }
        }

    }


    /**
     * 签名解析
     * @param userSignature JIRA获取的用户签名对象
     * @return 返回可渲染的图片对象
     */
    private PictureRenderData getPictureRender(Ao8de701UserSignature userSignature) {
        if (userSignature == null) {
            return null;
        }

        // 解码 Base64 字符串
        byte[] imageBytes = Base64.getDecoder().decode(userSignature.getSignaturePicture().trim());
        // 获取图片宽高
        List<Integer> size = getScaleSize(new ByteArrayInputStream(imageBytes), 120, 40);
        // 构造 PictureRenderData，指定宽度、高度和图片类型
        return new PictureRenderData(size.get(0), size.get(1), PictureType.suggestFileType(userSignature.getFileName()), new ByteArrayInputStream(imageBytes));
    }

    /**
     * 测试失效描述图片JSON解析
     * @param pictureListJson 测试失效描述图片JSON
     * @return 返回可渲染的图片对象列表
     */
    private List<PictureRenderData> getPictureRenderList(String pictureListJson) {
        List<PictureRenderData> pictureRenderList = new ArrayList<>();

        if (StrUtil.isEmpty(pictureListJson)) {
            return pictureRenderList;
        }

        List<JSONObject> pictureObjList = JSONObject.parseArray(pictureListJson, JSONObject.class);
        for (JSONObject pictureObj : pictureObjList) {
            Long sysFileId = pictureObj.getLong("id");

            SysFileInfo fileInfo = sysFileInfoService.getById(sysFileId);
            if (fileInfo != null) {
                try (
                        InputStream inputStream = minioClient.getObject(
                                GetObjectArgs.builder()
                                        .bucket(fileInfo.getFileBucket())
                                        .object(fileInfo.getFileObjectName())
                                        .build()
                        )
                ) {
                    if (inputStream != null) {
                        String name = pictureObj.getString("name");

                        // 通过内存暂存流数据，支持多次使用
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        byte[] bufferArr = new byte[1024];
                        int readNum;
                        while ((readNum = inputStream.read(bufferArr)) > 0) {
                            outputStream.write(bufferArr, 0, readNum);
                        }
                        byte[] byteArray = outputStream.toByteArray();

                        List<Integer> size = getScaleSize(new ByteArrayInputStream(byteArray), 16 * 37.8, 15 * 37.8);
                        PictureRenderData renderData = new PictureRenderData(size.get(0), size.get(1), PictureType.suggestFileType(name), new ByteArrayInputStream(byteArray));
                        pictureRenderList.add(renderData);
                    }
                } catch (Exception e) {
                    log.error(">>> 测试失效描述图片解析转换异常，sysFileId为：{}，具体信息为：{}", sysFileId, e.getMessage());
                }
            }

        }

        return pictureRenderList;
    }

    /**
     * 若图片尺寸正常，获取原width和height；若图片超过最大宽高，会进行图片缩放，获取缩放后的width和height
     * @param inputStream 图片数据输入流
     * @param maxWidth 最大宽
     * @param maxHeight 最大高
     * @return [width, height]
     */
    private List<Integer> getScaleSize(InputStream inputStream, double maxWidth, double maxHeight) {
        List<Integer> result = Arrays.asList(150, 150);
        try {
            BufferedImage image = ImageIO.read(inputStream);
            if (image != null) {
                int oriWidth = image.getWidth();
                int oriHeight = image.getHeight();
                result.set(0, oriWidth);
                result.set(1, oriHeight);

                if (oriWidth > maxWidth || oriHeight > maxHeight) {
                    double widthRatio = maxWidth / oriWidth;
                    double heightRatio = maxHeight / oriHeight;
                    double ratio = Math.min(widthRatio, heightRatio);
                    // 计算缩放后的宽高
                    int scaledWidth = (int) (oriWidth * ratio);
                    int scaledHeight = (int) (oriHeight * ratio);
                    result.set(0, scaledWidth);
                    result.set(1, scaledHeight);
                }
            }
        } catch (IOException e) {
            log.error(">>> 测试失效描述图片宽高设置异常：{}", e.getMessage());
        }

        return result;
    }

    /**
     * 测试失效事态升级处理，执行器调用
     */
    @Override
    public void failureEscalateHandle() {
        // 获取测试失效事态升级字典配置
        Map<String, String> dictDataMap = dictDataService.getDictDataByType("test_failure_escalate_config");
        String firstDayOfHigh = dictDataMap.getOrDefault("firstLevelDayOfHigh", "7");
        String firstDayOfNormal = dictDataMap.getOrDefault("firstLevelDayOfNormal", "14");
        String secondDay = dictDataMap.getOrDefault("secondLevelDay", "60");
        String thirdDay = dictDataMap.getOrDefault("thirdLevelDay", "90");
        String secondUser = dictDataMap.get("secondLevelUser");
        String thirdUser = dictDataMap.get("thirdLevelUser");
        String emailURL = dictDataMap.get("emailURL");

        // 事态升级时间节点计算
        LocalDate now = LocalDate.now();
        LocalDate firstOfHighDate = now.minusDays(Integer.parseInt(firstDayOfHigh));
        LocalDate firstOfNormalDate = now.minusDays(Integer.parseInt(firstDayOfNormal));
        LocalDate secondDate = now.minusDays(Integer.parseInt(secondDay));
        LocalDate thirdDate = now.minusDays(Integer.parseInt(thirdDay));

        // 需要发邮件的数据，查父级即可 父级ID-parentId为1、发送标志为0、审核状态reviewStatus为2、整体FA状态overallFaStatus、确认时间recordPassDate、项目等级projectLevel

        // 一级事态升级
        LambdaQueryWrapper<DpvTestFailureRecord> failureWrapper1 = new LambdaQueryWrapper<>();
        failureWrapper1.eq(DpvTestFailureRecord::getParentId, 1);
        failureWrapper1.eq(DpvTestFailureRecord::getFirstSentStatus, 0);
        failureWrapper1.eq(DpvTestFailureRecord::getReviewStatus, "2");
        failureWrapper1.and(wrapper -> wrapper.ne(DpvTestFailureRecord::getOverallFaStatus, "finish").or().isNull(DpvTestFailureRecord::getOverallFaStatus));
        failureWrapper1.gt(DpvTestFailureRecord::getRecordPassDate, secondDate);
        failureWrapper1.and(wrapper -> wrapper
                .in(DpvTestFailureRecord::getProjectLevel, "S", "A").le(DpvTestFailureRecord::getRecordPassDate, firstOfHighDate)
                .or()
                .in(DpvTestFailureRecord::getProjectLevel, "B", "C").le(DpvTestFailureRecord::getRecordPassDate, firstOfNormalDate)
        );
        List<DpvTestFailureRecord> firstFailureList = this.list(failureWrapper1);
        Map<Boolean, List<DpvTestFailureRecord>> firstTestTypeMap = firstFailureList.stream().collect(Collectors.groupingBy(item -> "产品鉴定测试".equals(item.getTestType()) || "应用边界测试".equals(item.getTestType())));
        // 产品鉴定测试&应用边界测试：升级责任人-测试部门长（即部门经理），升级对象-科研副院长及所长
        if (firstTestTypeMap.containsKey(true)) {
            List<DpvTestFailureRecord> firstCateOneList = firstTestTypeMap.get(true);
            Map<String, List<DpvTestFailureRecord>> deptManagerMap = firstCateOneList.stream().collect(Collectors.groupingBy(DpvTestFailureRecord::getDepartmentManager));
            for (String deptManager : deptManagerMap.keySet()) {
                sendFailureEscalateMsg(deptManager, "科研副院长及所长", deptManagerMap.get(deptManager), 1, emailURL);
            }
        }
        // 研发测试&产品验证测试：升级责任人-实验室负责人，升级对象-部门长（即部门经理）
        if (firstTestTypeMap.containsKey(false)) {
            List<DpvTestFailureRecord> firstCateTwoList = firstTestTypeMap.get(false);
            Map<String, List<DpvTestFailureRecord>> firstsysfzrMap = firstCateTwoList.stream().collect(Collectors.groupingBy(DpvTestFailureRecord::getLaboratoryResponsible));
            for (String sysfzr : firstsysfzrMap.keySet()) {
                sendFailureEscalateMsg(sysfzr, "部门长", firstsysfzrMap.get(sysfzr), 1, emailURL);
            }
        }

        // 二级事态升级
        LambdaQueryWrapper<DpvTestFailureRecord> failureWrapper2 = new LambdaQueryWrapper<>();
        failureWrapper2.eq(DpvTestFailureRecord::getParentId, 1);
        failureWrapper2.eq(DpvTestFailureRecord::getSecondSentStatus, 0);
        failureWrapper2.eq(DpvTestFailureRecord::getReviewStatus, "2");
        failureWrapper2.and(wrapper -> wrapper.ne(DpvTestFailureRecord::getOverallFaStatus, "finish").or().isNull(DpvTestFailureRecord::getOverallFaStatus));
        failureWrapper2.gt(DpvTestFailureRecord::getRecordPassDate, thirdDate).le(DpvTestFailureRecord::getRecordPassDate, secondDate);
        List<DpvTestFailureRecord> secondFailureList = this.list(failureWrapper2);
        Map<Boolean, List<DpvTestFailureRecord>> secondTestTypeMap = secondFailureList.stream().collect(Collectors.groupingBy(item -> "产品鉴定测试".equals(item.getTestType()) || "应用边界测试".equals(item.getTestType())));
        // 产品鉴定测试&应用边界测试：升级责任人-科研副院长（字典配置），升级对象-院长及质量副院长
        if (secondTestTypeMap.containsKey(true)) {
            List<DpvTestFailureRecord> secondCateOneList = secondTestTypeMap.get(true);
            if (StrUtil.isNotEmpty(secondUser)) {
                sendFailureEscalateMsg(secondUser, "院长及质量副院长", secondCateOneList, 2, emailURL);
            }
        }
        // 研发测试&产品验证测试：升级责任人-实验室负责人，升级对象-所长
        if (secondTestTypeMap.containsKey(false)) {
            List<DpvTestFailureRecord> secondCateTwoList = secondTestTypeMap.get(false);
            Map<String, List<DpvTestFailureRecord>> secondsysfzrMap = secondCateTwoList.stream().collect(Collectors.groupingBy(DpvTestFailureRecord::getLaboratoryResponsible));
            for (String sysfzr : secondsysfzrMap.keySet()) {
                sendFailureEscalateMsg(sysfzr, "所长", secondsysfzrMap.get(sysfzr), 2, emailURL);
            }
        }

        // 三级事态升级
        LambdaQueryWrapper<DpvTestFailureRecord> failureWrapper3 = new LambdaQueryWrapper<>();
        failureWrapper3.eq(DpvTestFailureRecord::getParentId, 1);
        failureWrapper3.eq(DpvTestFailureRecord::getThirdSentStatus, 0);
        failureWrapper3.eq(DpvTestFailureRecord::getReviewStatus, "2");
        failureWrapper3.and(wrapper -> wrapper.ne(DpvTestFailureRecord::getOverallFaStatus, "finish").or().isNull(DpvTestFailureRecord::getOverallFaStatus));
        failureWrapper3.le(DpvTestFailureRecord::getRecordPassDate, thirdDate);
        List<DpvTestFailureRecord> thirdFailureList = this.list(failureWrapper3);
        Map<Boolean, List<DpvTestFailureRecord>> thirdTestTypeMap = thirdFailureList.stream().collect(Collectors.groupingBy(item -> "产品鉴定测试".equals(item.getTestType()) || "应用边界测试".equals(item.getTestType())));
        // 产品鉴定测试&应用边界测试：升级责任人-质量副院长（字典配置），升级对象-质量副总裁
        if (thirdTestTypeMap.containsKey(true)) {
            List<DpvTestFailureRecord> thirdCateOneList = thirdTestTypeMap.get(true);
            if (StrUtil.isNotEmpty(thirdUser)) {
                sendFailureEscalateMsg(thirdUser, "质量副总裁", thirdCateOneList, 3, emailURL);
            }
        }
        // 研发测试&产品验证测试：升级责任人-实验室负责人，升级对象-院长
        if (thirdTestTypeMap.containsKey(false)) {
            List<DpvTestFailureRecord> thirdCateTwoList = thirdTestTypeMap.get(false);
            Map<String, List<DpvTestFailureRecord>> thirdsysfzrMap = thirdCateTwoList.stream().collect(Collectors.groupingBy(DpvTestFailureRecord::getLaboratoryResponsible));
            for (String sysfzr : thirdsysfzrMap.keySet()) {
                sendFailureEscalateMsg(sysfzr, "院长", thirdsysfzrMap.get(sysfzr), 3, emailURL);
            }
        }

    }

    /**
     * 发送测试失效事态升级邮件，更新发送状态字段
     * @param toUserAccount 邮件接收对象，即升级责任人
     * @param targetUserRole 升级对象
     * @param failureRecordList 测试失效记录列表
     * @param failureLevel 事态升级级别
     * @param emailURL 邮件跳转URL
     */
    private void sendFailureEscalateMsg(String toUserAccount, String targetUserRole, List<DpvTestFailureRecord> failureRecordList, int failureLevel, String emailURL) {
        if (CollectionUtil.isEmpty(failureRecordList)) {
            return;
        }

        // 邮件内容
        /**
         * 您好！
         * 经核实，以下项目在测试过程中出现失效问题，根据流程现需升级至 *** ，烦请升级推进解决：
         * 表格内容：序号，登记人，项目名称，项目阶段，批次号，测试大类，失效类别，测试失效描述，失效登记编号
         * 以上，谢谢！
         */
        StringBuilder msgBuilder = new StringBuilder();
        msgBuilder.append("您好！<br />");
        msgBuilder.append("<br />");
        msgBuilder.append("经核实，以下项目在测试过程中出现失效问题，根据流程现需升级至").append(targetUserRole).append("，烦请升级推进解决：<br />");
        msgBuilder.append("<br />");

        msgBuilder.append("<table border=\"1\" cellpadding=\"1\" cellspacing=\"1\">");
        msgBuilder.append("<tbody>");
        msgBuilder.append("<tr>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">序号&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">登记人&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">产品名称&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">项目名称&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">项目阶段&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">批次号&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">测试项目&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">失效类别&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">失效发生日期&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">测试失效描述&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">失效登记编号&nbsp;</th>");
        msgBuilder.append("</tr>");
        for (int i = 0; i < failureRecordList.size(); i++) {
            DpvTestFailureRecord failureRecord = failureRecordList.get(i);

            String failureCate = failureCateMap.getOrDefault(failureRecord.getFailureCate(), "");
            if ("起火".equals(failureCate)) {
                failureCate = "起 火";
            }
            String fileCode = failureRecord.getFileCode();
            String href = fileCode;
            if (StrUtil.isNotEmpty(fileCode)) {
                try {
                    href = "<a href=\"" + emailURL + "?fileCode=" + URLEncoder.encode(fileCode, StandardCharsets.UTF_8.toString()) + "\" target=\"_blank\">" + fileCode + "</a>";
                } catch (UnsupportedEncodingException e) {
                    log.error(">>> 测试失效登记编号编码异常：{}", e.getMessage());
                }
            }

            msgBuilder.append("<tr>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(i + 1).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getFolderUserName()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getProductName()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getProjectName()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getTestSampleStage()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getCellBatch()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getTestProjectName()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureCate).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(Objects.toString(DateUtil.format(failureRecord.getFailureDate(), "yyyy-MM-dd"), "")).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: left;\">").append(failureRecord.getTestFailureDescription()).append("&nbsp;</th>");
            msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(href).append("&nbsp;</th>");
            msgBuilder.append("</tr>");
        }
        msgBuilder.append("</tbody>");
        msgBuilder.append("</table><br />");

        msgBuilder.append("<br />");
        msgBuilder.append("以上，谢谢！");

        // 配置邮件信息
        SysUser sysUser = sysUserService.getUserByCount(toUserAccount);

        String emailSubject = "测试失效事态升级提醒";
        String recipientEmail = sysUser.getEmail();
        String emailContent = msgBuilder.toString();

        // 创建邮件发送记录
        String businessIds = failureRecordList.stream().map(item -> item.getId() + "").collect(Collectors.joining(","));
        EmailSendRecord emailRecord = EmailSendRecord.builder()
                .businessType("测试失效事态升级" + failureLevel + "级")
                .businessIds(businessIds)
                .emailSubject(emailSubject)
                .senderEmail(EMAIL_CONFIGS.getFrom())
                .recipientEmails(recipientEmail)
                .emailContent(emailContent)
                .sendStatus(0) // 待发送
                .build();

        // 先记录邮件发送记录
        emailSendRecordService.recordEmailSend(emailRecord);

        SendMailParam mailParam = SendMailParam.builder()
                .to(recipientEmail)
                .title(emailSubject)
                .content(emailContent)
                .build();

        // 发送邮件
        try {
            mailSender.sendMailHtml(mailParam);
            // 更新发送状态为成功
            emailSendRecordService.updateSendStatus(emailRecord.getId(), 1, null);
        } catch (Exception e) {
            // 更新发送状态为失败
            emailSendRecordService.updateSendStatus(emailRecord.getId(), 2, e.getMessage());
            throw e; // 重新抛出异常，保持原有逻辑
        }

        // 邮件发送成功需要更新发送标志
        LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(DpvTestFailureRecord::getId, failureRecordList.stream().map(DpvTestFailureRecord::getId).collect(Collectors.toList()));
        if (failureLevel == 1) {
            updateWrapper.set(DpvTestFailureRecord::getFirstSentStatus, 1);
        } else if (failureLevel == 2) {
            updateWrapper.set(DpvTestFailureRecord::getSecondSentStatus, 1);
        } else if (failureLevel == 3) {
            updateWrapper.set(DpvTestFailureRecord::getThirdSentStatus, 1);
        }
        this.update(updateWrapper);

    }

    /**
     * 测试失效登记完成后给所有过流程的人员发邮件（登记第三部分“流程审核”中的所有人员，院长除外），增加发起人
     * @param failureRecord 测试失效记录
     */
    private void sendFailureRecordMsg(DpvTestFailureRecord failureRecord) {
        Set<String> toAccountSet = new HashSet<>();
        if ("1".equals(JiraApiParams.isOnline)) {
            String countersigner = failureRecord.getCountersigner();
            if (StrUtil.isNotEmpty(countersigner)) {
                toAccountSet.addAll(Arrays.asList(countersigner.split(",")));
            }
            toAccountSet.add(failureRecord.getSampleManager());
            toAccountSet.add(failureRecord.getLaboratoryResponsible());
            toAccountSet.add(failureRecord.getDepartmentManager());
            toAccountSet.add(failureRecord.getProductManager());
            toAccountSet.add(failureRecord.getProductMajordomo());
            toAccountSet.add(failureRecord.getDQE());
            toAccountSet.add(failureRecord.getHeadOfTheInstitute());
            toAccountSet.add(failureRecord.getVicePresident());
            toAccountSet.add(failureRecord.getInitiatorAccount());
        } else {//测试环境，仅通知发起人
            toAccountSet.add(failureRecord.getInitiatorAccount());
        }
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getAccount, toAccountSet);
        queryWrapper.ne(SysUser::getStatus, CommonStatusEnum.DELETED.getCode());
        List<SysUser> sysUserList = sysUserService.list(queryWrapper);
        Set<String> toEmailSet = sysUserList.stream().map(SysUser::getEmail).collect(Collectors.toSet());

        // 邮件内容
        /**
         * 收件好，
         * 项目名称&项目阶段&测试类别&失效类别&测试失效描述，以上，请各位知悉。
         */
        String failureCate = failureCateMap.getOrDefault(failureRecord.getFailureCate(), "");
        if ("起火".equals(failureCate)) {
            failureCate = "起 火";
        }
        StringBuilder msgBuilder = new StringBuilder();
        msgBuilder.append("收件好，<br />");
        msgBuilder.append("<br />");
        msgBuilder.append("<table border=\"1\" cellpadding=\"1\" cellspacing=\"1\">");
        msgBuilder.append("<tbody>");
        msgBuilder.append("<tr>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">产品名称&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">项目名称&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">项目阶段&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">测试项目&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">失效类别&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">失效发生日期&nbsp;</th>");
        msgBuilder.append("<th style=\"text-align: center; white-space: nowrap;\">测试失效描述&nbsp;</th>");
        msgBuilder.append("</tr>");
        msgBuilder.append("<tr>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getProductName()).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getProjectName()).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getTestSampleStage()).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureRecord.getTestProjectName()).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(failureCate).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: center; white-space: nowrap;\">").append(Objects.toString(DateUtil.format(failureRecord.getFailureDate(), "yyyy-MM-dd"), "")).append("&nbsp;</th>");
        msgBuilder.append("<td style=\"text-align: left;\">").append(failureRecord.getTestFailureDescription()).append("&nbsp;</th>");
        msgBuilder.append("</tr>");
        msgBuilder.append("</tbody>");
        msgBuilder.append("</table>");
        msgBuilder.append("<br />");
        msgBuilder.append("以上，请各位知悉。");

        // 附件增加失效告知书
        String fileId = failureRecord.getFileId();
        SysFileInfo fileInfo = sysFileInfoService.getById(fileId);
        if (fileInfo == null) {
            log.error(">>> 测试失效登记完成发送邮件异常，id为：{}，fileCode为：{}，具体信息为：{}", failureRecord.getId(), failureRecord.getFileCode(), "找不到失效告知书文件");
            return;
        }
        String bucketPath = FileConfig.TEST_FAILURE_NOTICE_FILE + File.separator + fileId;
        String tempFileName = fileInfo.getFileOriginName();
        try {
            if (!fileOperator.isExistingFile(bucketPath, tempFileName)) {
                String tempFilePath = fileOperator.newEmptyFile(bucketPath, tempFileName);
                minioClient.downloadObject(
                        DownloadObjectArgs.builder()
                                .bucket(fileInfo.getFileBucket())
                                .object(fileInfo.getFileObjectName())
                                .filename(tempFilePath)
                                .build()
                );
            }

            // 创建邮件发送记录
            EmailSendRecord emailRecord = EmailSendRecord.builder()
                    .businessType("测试失效登记完成")
                    .businessIds(failureRecord.getId() + "")
                    .emailSubject("测试失效登记提醒")
                    .senderEmail(EMAIL_CONFIGS.getFrom())
                    .recipientEmails(String.join(",", toEmailSet))
                    .emailContent(msgBuilder.toString())
                    .attachments(fileInfo.getId() + "")
                    .sendStatus(0) // 待发送
                    .build();

            // 先记录邮件发送记录
            emailSendRecordService.recordEmailSend(emailRecord);

            // 配置邮件信息
            MailAccount mailAccount = new MailAccount();
            BeanUtil.copyProperties(EMAIL_CONFIGS, mailAccount);

            // 发送邮件
            try {
                MailUtil.send(mailAccount, toEmailSet, "测试失效登记提醒", msgBuilder.toString(), true, fileOperator.getFile(bucketPath, tempFileName));
                // 更新发送状态为成功
                emailSendRecordService.updateSendStatus(emailRecord.getId(), 1, null);
            } catch (Exception e) {
                // 更新发送状态为失败
                emailSendRecordService.updateSendStatus(emailRecord.getId(), 2, e.getMessage());
                throw e; // 重新抛出异常，保持原有逻辑
            }
        } catch (Exception e) {
            log.error(">>> 测试失效登记完成发送邮件异常，id为：{}，fileCode为：{}，具体信息为：{}", failureRecord.getId(), failureRecord.getFileCode(), e.getMessage());
        } finally {
            // 删除临时文件夹
            fileOperator.deleteFile(bucketPath, "");
        }
    }

    private List<SysDictData> getDictByCode(String code) {
        LambdaQueryWrapper<SysDictType> dictTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dictTypeLambdaQueryWrapper.ne(SysDictType::getStatus, CommonStatusEnum.DELETED.getCode());
        dictTypeLambdaQueryWrapper.eq(SysDictType::getCode, code);
        SysDictType sysDictType = dictTypeService.getOne(dictTypeLambdaQueryWrapper);
        LambdaQueryWrapper<SysDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDictData::getTypeId, sysDictType.getId());
        return dictDataService.list(queryWrapper);
    }

    @Override
    public DpvTestFailureRecord getCellMsg(TsetFailureQueryParam param) {
//        JSONObject jsonObject = new JSONObject();
        DpvTestFailureRecord dpvTestFailureRecord = new DpvTestFailureRecord();
        String cellCode = param.getCellCode();
        LambdaQueryWrapper<TLimsOrder> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dpvTestFailureRecordLambdaQueryWrapper.eq(TLimsOrder::getCelltestcode, cellCode);
        List<TLimsOrder> orderList = itLimsOrderService.list(dpvTestFailureRecordLambdaQueryWrapper);
        if (orderList.isEmpty()) {
            return dpvTestFailureRecord;
        }
        TLimsOrder tLimsOrder = orderList.get(0);
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        LeaderRelation leaderRelation = new LeaderRelation();
        leaderRelation.setAccount(sysLoginUser.getAccount());
        List<LeaderRelation> leaderByUser = sysUserService.getLeaderByUser(leaderRelation);
        leaderByUser = leaderByUser.stream().filter(e -> "600".equals(e.getLeaderType())).collect(Collectors.toList());
        //委托单信息
        LambdaQueryWrapper<TLimsFolder> folderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        folderLambdaQueryWrapper.eq(TLimsFolder::getId, tLimsOrder.getFolderid());

        TLimsFolder limsFolder = itLimsFolderService.getOne(folderLambdaQueryWrapper);
        String laboratoryid = limsFolder.getLaboratoryid();
        String testarea = limsFolder.getTestarea();
        String testCate = "";
        if ("HZ_YJ_DL_CS".equals(laboratoryid)) {
            if (testarea != null && testarea.contains("/")) {
                String[] split = testarea.split("/");
                testCate = split[1];
                testCate = testCate.replace("测试", ""); // 第六实验室：testCate关联测试区域二级，去掉"测试"
            }
        }else if ("HZ_YJ_DL_AQ".equals(laboratoryid)) {
            testCate = "安全";
        }else if ("HZ_YJ_DL_JM".equals(laboratoryid)) {
            testCate = "精密";
        }

        String productid = limsFolder.getProductid();
        String productName = limsFolder.getProducttype();
        String technicalstatus = limsFolder.getTechnicalstatus();
        String technicalstatusnumber = limsFolder.getTechnicalstatusnumber();

        //测试项目信息
        LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrderid, tLimsOrder.getId());
        List<TLimsTestmatrix> limsTestmatrixList = itLimsTestmatrixService.list(testmatrixLambdaQueryWrapper);
        List<Long> ordtaskIdList = limsTestmatrixList.stream().map(TLimsTestmatrix::getOrdtaskid).collect(Collectors.toList());

        LambdaQueryWrapper<TLimsOrdtask> ordtaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
        ordtaskLambdaQueryWrapper.in(TLimsOrdtask::getId, ordtaskIdList);
        List<TLimsOrdtask> ordtaskList = itLimsOrdtaskService.list(ordtaskLambdaQueryWrapper);
        String testProjectName = ordtaskList.stream().map(TLimsOrdtask::getAlias).collect(Collectors.joining(";"));

        //JIRA产品信息
        LambdaQueryWrapper<ProductManager> productManagerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (productid != null) {
            productManagerLambdaQueryWrapper.eq(ProductManager::getIssueId, productid);
        } else {
            productManagerLambdaQueryWrapper.like(ProductManager::getProductName, productName);
        }
        productManagerLambdaQueryWrapper.eq(ProductManager::getProductOrProject, 1L);
        List<ProductManager> productList = iProductManagerService.list(productManagerLambdaQueryWrapper);
        ProductManager product = productList.isEmpty() ? null : productList.get(0);

        dpvTestFailureRecord.setProductId(Long.valueOf(productid == null ? "0" : productid));
        dpvTestFailureRecord.setProductDepartment(product == null ? limsFolder.getCreatedbyorgname(): String.valueOf(product.getParentDept()));
        dpvTestFailureRecord.setOrderType(limsFolder.getCylindercellflag());
        dpvTestFailureRecord.setProductName(productName);
        dpvTestFailureRecord.setProjectLevel(product==null ? null : product.getProjectLevelName());
        dpvTestFailureRecord.setResearchStage(technicalstatus);
        dpvTestFailureRecord.setTestSampleStage(technicalstatus.charAt(0) + (technicalstatusnumber == null ? "0" : technicalstatusnumber));
        dpvTestFailureRecord.setTestCate(testCate);
        dpvTestFailureRecord.setFolderNo(limsFolder.getFolderno());
        dpvTestFailureRecord.setTestProjectName(testProjectName);
        dpvTestFailureRecord.setCellCode(tLimsOrder.getCelltestcode());
        dpvTestFailureRecord.setCellBatch(limsFolder.getBatchnumber());
        //用户信息
        if (!leaderByUser.isEmpty()) {
            dpvTestFailureRecord.setDepartmentManager(leaderByUser.get(0).getLeaderAccount());
        }
        dpvTestFailureRecord.setProductManager(product==null ? null : product.getProductManager());
        dpvTestFailureRecord.setProductMajordomo(product==null ? null : product.getProductTechMajordomo());
        dpvTestFailureRecord.setDQE(product==null ? null : product.getProductDQE());
        dpvTestFailureRecord.setHeadOfTheInstitute(product==null ? null : product.getHeadOfTheInstitute());
//        dpvTestFailureRecord.setVicePresident(product==null ? null : product.getVicePresident());
//        dpvTestFailureRecord.setPresident(product==null ? null : product.getPresident());
        dpvTestFailureRecord.setProductId(product == null ? null : product.getId());
        List<SysDictData> sysDictDataList = dictDataService.list(SysDictDataParam.builder().typeId(1951124302323834881L).build());
        Optional<SysDictData> optionalAQ = sysDictDataList.stream().filter(e -> "sampleManagerAQ".equals(e.getCode())).findFirst();
        String sampleManagerAQ = optionalAQ.isPresent() ? optionalAQ.get().getValue() : "121082";
        Optional<SysDictData> optionalDL = sysDictDataList.stream().filter(e -> "sampleManagerDL".equals(e.getCode())).findFirst();
        String sampleManagerDL = optionalDL.isPresent() ? optionalDL.get().getValue() : "110539";
        Optional<SysDictData> optionalAQ2 = sysDictDataList.stream().filter(e -> "laboratoryResponsibleAQ".equals(e.getCode())).findFirst();
        String labResponsibleAQ = optionalAQ2.isPresent() ? optionalAQ2.get().getValue() : "059166";
        Optional<SysDictData> optionalDL2 = sysDictDataList.stream().filter(e -> "laboratoryResponsibleDL".equals(e.getCode())).findFirst();
        String labResponsibleDL = optionalDL2.isPresent() ? optionalDL2.get().getValue() : "041092";
        dpvTestFailureRecord.setSampleManager("HZ_YJ_DL_CS".equals(laboratoryid) ? sampleManagerDL : sampleManagerAQ);
        dpvTestFailureRecord.setLaboratoryResponsible("HZ_YJ_DL_CS".equals(laboratoryid) ? labResponsibleDL : labResponsibleAQ);

        return dpvTestFailureRecord;
    }

    @Override
    public DpvTestFailureRecord getFolderMsg(TsetFailureQueryParam param) {
//        JSONObject jsonObject = new JSONObject();
        DpvTestFailureRecord dpvTestFailureRecord = new DpvTestFailureRecord();
//        String cellCode = param.getCellCode();
//        LambdaQueryWrapper<TLimsOrder> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        dpvTestFailureRecordLambdaQueryWrapper.eq(TLimsOrder::getCellcode, cellCode);
//        List<TLimsOrder> orderList = itLimsOrderService.list(dpvTestFailureRecordLambdaQueryWrapper);
//        if (orderList.isEmpty()) {
//            return dpvTestFailureRecord;
//        }
//        TLimsOrder tLimsOrder = orderList.get(0);
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        LeaderRelation leaderRelation = new LeaderRelation();
        leaderRelation.setAccount(sysLoginUser.getAccount());
        List<LeaderRelation> leaderByUser = sysUserService.getLeaderByUser(leaderRelation);
        leaderByUser = leaderByUser.stream().filter(e -> "600".equals(e.getLeaderType())).collect(Collectors.toList());
        //委托单信息
        LambdaQueryWrapper<TLimsFolder> folderLambdaQueryWrapper = new LambdaQueryWrapper<>();
        folderLambdaQueryWrapper.eq(TLimsFolder::getFolderno, param.getFolderNo());

        TLimsFolder limsFolder = itLimsFolderService.getOne(folderLambdaQueryWrapper);

        String laboratoryid = limsFolder.getLaboratoryid();
        String testarea = limsFolder.getTestarea();
        String testCate = "";
        if ("HZ_YJ_DL_CS".equals(laboratoryid)) {
            if (testarea != null && testarea.contains("/")) {
                String[] split = testarea.split("/");
                testCate = split[1];
                testCate = testCate.replace("测试", ""); // 第六实验室：testCate关联测试区域二级，去掉"测试"
            }
        }else if ("HZ_YJ_DL_AQ".equals(laboratoryid)) {
            testCate = "安全";
        }else if ("HZ_YJ_DL_JM".equals(laboratoryid)) {
            testCate = "精密";
        }

        String productid = limsFolder.getProductid();
        String productName = limsFolder.getProducttype();
        String projectName = limsFolder.getProjectname();
        String technicalstatus = limsFolder.getTechnicalstatus();
        String technicalstatusnumber = limsFolder.getTechnicalstatusnumber();

        //测试项目信息
//        LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrderid, tLimsOrder.getId());
//        List<TLimsTestmatrix> limsTestmatrixList = itLimsTestmatrixService.list(testmatrixLambdaQueryWrapper);
//        List<Long> ordtaskIdList = limsTestmatrixList.stream().map(TLimsTestmatrix::getOrdtaskid).collect(Collectors.toList());
//
//        LambdaQueryWrapper<TLimsOrdtask> ordtaskLambdaQueryWrapper = new LambdaQueryWrapper<>();
//        ordtaskLambdaQueryWrapper.in(TLimsOrdtask::getId, ordtaskIdList);
//        List<TLimsOrdtask> ordtaskList = itLimsOrdtaskService.list(ordtaskLambdaQueryWrapper);
//        String testProjectName = ordtaskList.stream().map(TLimsOrdtask::getAlias).collect(Collectors.joining(";"));

        String batchnumber = limsFolder.getBatchnumber();
        //使用正则表达式校验，需要满足以下条件，4位数字或字母组成的字符
        if (StringUtils.isNotEmpty(batchnumber) && batchnumber.matches("^[0-9A-Za-z]{4}$")) {
            batchnumber = null;
        }

        List<SysDictData> sysDictDataList = dictDataService.list(SysDictDataParam.builder().typeId(1951124302323834881L).build());
        Optional<SysDictData> optionalAQ = sysDictDataList.stream().filter(e -> "sampleManagerAQ".equals(e.getCode())).findFirst();
        String sampleManagerAQ = optionalAQ.isPresent() ? optionalAQ.get().getValue() : "121082";
        Optional<SysDictData> optionalDL = sysDictDataList.stream().filter(e -> "sampleManagerDL".equals(e.getCode())).findFirst();
        String sampleManagerDL = optionalDL.isPresent() ? optionalDL.get().getValue() : "110539";
        Optional<SysDictData> optionalAQ2 = sysDictDataList.stream().filter(e -> "laboratoryResponsibleAQ".equals(e.getCode())).findFirst();
        String labResponsibleAQ = optionalAQ2.isPresent() ? optionalAQ2.get().getValue() : "059166";
        Optional<SysDictData> optionalDL2 = sysDictDataList.stream().filter(e -> "laboratoryResponsibleDL".equals(e.getCode())).findFirst();
        String labResponsibleDL = optionalDL2.isPresent() ? optionalDL2.get().getValue() : "041092";
        dpvTestFailureRecord.setSampleManager("HZ_YJ_DL_CS".equals(laboratoryid) ? sampleManagerDL : sampleManagerAQ);
        dpvTestFailureRecord.setLaboratoryResponsible("HZ_YJ_DL_CS".equals(laboratoryid) ? labResponsibleDL : labResponsibleAQ);
        dpvTestFailureRecord.setLaboratoryId(laboratoryid);
        dpvTestFailureRecord.setOrderType(limsFolder.getCylindercellflag());
        dpvTestFailureRecord.setProductName(productName);
        dpvTestFailureRecord.setResearchStage(technicalstatus);
        dpvTestFailureRecord.setTestSampleStage(ObjectUtil.isEmpty(technicalstatus)?null:technicalstatus.charAt(0) + (technicalstatusnumber == null ? "0" : technicalstatusnumber));
        dpvTestFailureRecord.setTestCate(testCate);
        dpvTestFailureRecord.setFolderNo(limsFolder.getFolderno());
        dpvTestFailureRecord.setTestType(limsFolder.getTesttype());
//        dpvTestFailureRecord.setTestProjectName(testProjectName);//页面选择
//        dpvTestFailureRecord.setCellCode(tLimsOrder.getCellcode());//页面选择
        dpvTestFailureRecord.setCellBatch(batchnumber);
        dpvTestFailureRecord.setFolderDepartment(limsFolder.getCreatedbyorgname());
        dpvTestFailureRecord.setFolderUserAccount(limsFolder.getCreatedbyid());
        dpvTestFailureRecord.setFolderUserName(limsFolder.getCreatedbyname());
        //用户信息
        if (!leaderByUser.isEmpty()) {
            dpvTestFailureRecord.setDepartmentManager(leaderByUser.get(0).getLeaderAccount());
        }

        //JIRA产品信息
        LambdaQueryWrapper<ProductManager> productManagerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (productid != null) {
            productManagerLambdaQueryWrapper.eq(ProductManager::getIssueId, productid);
        } else if (StrUtil.isNotEmpty(productName)) {
            productManagerLambdaQueryWrapper.like(ProductManager::getProductName, productName);
        } else {
            productManagerLambdaQueryWrapper.eq(ProductManager::getProjectName, projectName);
        }
        productManagerLambdaQueryWrapper.eq(ProductManager::getProductOrProject, 1L);
        List<ProductManager> productList = iProductManagerService.list(productManagerLambdaQueryWrapper);
        ProductManager product = productList.isEmpty() ? null : productList.get(0);
        if (product == null) {
            return dpvTestFailureRecord;
        } else {
            dpvTestFailureRecord.setProductName(product.getProductName());
        }
        dpvTestFailureRecord.setProductId(Long.valueOf(product == null ? "0" : product.getIssueId().toString()));
        dpvTestFailureRecord.setProductDepartment(product == null ? limsFolder.getCreatedbyorgname(): String.valueOf(product.getParentDept()));
        dpvTestFailureRecord.setProjectLevel(product==null ? null : product.getProjectLevelName());
        dpvTestFailureRecord.setProductManager(product==null ? null : product.getProductManager());
        dpvTestFailureRecord.setProductMajordomo(product==null ? null : product.getProductTechMajordomo());
        dpvTestFailureRecord.setDQE(product==null ? null : product.getProductDQE());
        dpvTestFailureRecord.setHeadOfTheInstitute(product==null ? null : product.getHeadOfTheInstitute());
//        dpvTestFailureRecord.setVicePresident(product==null ? null : product.getVicePresident());
//        dpvTestFailureRecord.setPresident(product==null ? null : product.getPresident());
        dpvTestFailureRecord.setProductId(product == null ? null : product.getId());

        return dpvTestFailureRecord;
    }

    @Override
    public List<TLimsOrder> getTLimsOrdByOrdtaskId(TsetFailureQueryParam param) {
        String ordtaskId = param.getOrdtaskId();
        //测试项目信息
        LambdaQueryWrapper<TLimsTestmatrix> testmatrixLambdaQueryWrapper = new LambdaQueryWrapper<>();
        testmatrixLambdaQueryWrapper.eq(TLimsTestmatrix::getOrdtaskid, Long.valueOf(ordtaskId));
        List<TLimsTestmatrix> limsTestmatrixList = itLimsTestmatrixService.list(testmatrixLambdaQueryWrapper);
        List<Long> orderIdList = limsTestmatrixList.stream().map(TLimsTestmatrix::getOrderid).collect(Collectors.toList());

        LambdaQueryWrapper<TLimsOrder> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dpvTestFailureRecordLambdaQueryWrapper.in(TLimsOrder::getId, orderIdList);
        List<TLimsOrder> orderList = itLimsOrderService.list(dpvTestFailureRecordLambdaQueryWrapper);

        return orderList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiveTestFailureCheck(TestFailureReceiveFromJIRAParam param) throws Exception {

        Long businessId = param.getBusinessId();
//        Integer checkType = param.getCheckType();

        List<SysDictData> sysDictDataList = this.getDictByCode("JIRA_IssueTypeId");

        Optional<SysDictData> first = sysDictDataList.stream().filter(e -> e.getValue().equals(param.getIssueTypeId() + "")).findFirst();
        if (first.isPresent()) {
            String code = first.get().getCode();
            DpvTestFailureRecord dpvTestFailureRecord1 = null;
            if (businessId == null) {
                LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordQueryWrapper = new LambdaQueryWrapper<>();
                dpvTestFailureRecordQueryWrapper.eq(DpvTestFailureRecord::getDpvTestFailureRecordIssueKey, param.getIssueKey());
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFailureCellInStoreIssueKey, param.getIssueKey());
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFailureCellOutStoreIssueKey, param.getIssueKey());
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFaResponsibleAssignIssueKey, param.getIssueKey());
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFaAnalyseReportIssueKey, param.getIssueKey());
                dpvTestFailureRecordQueryWrapper.eq(DpvTestFailureRecord::getParentId, 1); // 确保取到父级ID
                dpvTestFailureRecord1 = this.getOne(dpvTestFailureRecordQueryWrapper);
            }
            DpvTestFailureRecord dpvTestFailureRecord = businessId == null ? dpvTestFailureRecord1 : this.getById(businessId);
            if (dpvTestFailureRecord == null) {
                throw new ServiceException(500, "该业务ID不存在：" + businessId);
            }
            businessId = dpvTestFailureRecord.getId();
//            DpvTestFailureRecord dpvTestFailureRecord = DpvTestFailureRecord.builder().id(businessId).build();
            //获取子级电芯
            boolean isUpdateChildren = false;
            LambdaQueryWrapper<DpvTestFailureRecord> childrenQueryWrapper = new LambdaQueryWrapper<>();
            childrenQueryWrapper.eq(DpvTestFailureRecord::getParentId, businessId);
            List<DpvTestFailureRecord> childrenList = this.list(childrenQueryWrapper);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String faResponsibleAccount = param.getFaResponsibleAccount();
            String faResponsibleName = param.getFaResponsibleName();
            String faDueDate = param.getFaDueDate();
            boolean isFAResponsibleAssign = false;
            if (ObjectUtil.isNotEmpty(faResponsibleAccount) && ObjectUtil.isNotEmpty(faDueDate)) {
                isFAResponsibleAssign = true;
            }

            // 增加驳回任务处理：JIRA传回handleResult，通过-10 驳回-20
            String issueKey = param.getIssueKey();
            if (Objects.equals(param.getHandleResult(), 20)) {
                if ("DPVTestFailureRecord".equals(code)) {
                    // PBIGLKF-1092 测试失效登记被驳回后需增加可编辑功能
                    LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(DpvTestFailureRecord::getId, businessId).or().eq(DpvTestFailureRecord::getParentId, businessId);
                    updateWrapper.set(DpvTestFailureRecord::getReviewStatus, "3");
                    this.update(updateWrapper);
                } else if ("FAAnalyseReport".equals(code)) {
                    // PBIGLKF-1096 分析报告流程优化
                    LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(DpvTestFailureRecord::getFaAnalyseReportIssueKey, issueKey);
                    updateWrapper.set(DpvTestFailureRecord::getFaStatus, "reviewRejected");
                    this.update(updateWrapper);
                } else if ("failureCellOutStore".equals(code)) {
                    // PBIGLKF-1122 出库流程添加驳回
                    LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(DpvTestFailureRecord::getFaAnalyseReportIssueKey, issueKey);
                    updateWrapper.set(DpvTestFailureRecord::getStockStatus, "inStore");
                    this.update(updateWrapper);
                }

                return true;
            }

            if ("DPVTestFailureRecord".equals(code)&& !isFAResponsibleAssign) {
                isUpdateChildren = true;
                //审核通过创建库存、创建FA失效分析报告
                if ("2".equals(dpvTestFailureRecord.getReviewStatus())) {
                    return true;//重复提交
                }
                dpvTestFailureRecord.setReviewStatus("4");
                dpvTestFailureRecord.setRecordPassDate(new Date());
                childrenList.forEach(e -> {
                    e.setReviewStatus("4");
                    e.setRecordPassDate(new Date());
                });

                // 生成带电子签名的失效告知书（通过代理提交新事务确保SysFileInfo存在）
                failureRecordService.setFailureNoticeFile(dpvTestFailureRecord, true, param.getReviewer(), param.getApprover());
                childrenList.forEach(item -> {
                    item.setFileName(dpvTestFailureRecord.getFileName());
                    item.setFileId(dpvTestFailureRecord.getFileId());
                });

                // dpvTestFailureRecord.setStockStatus("preInStore");
                //发起FA责任人指定jira流程，创建成功设置fa状态为待启动
/*                Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "FAResponsibleAssign".equals(e.getCode())).findFirst();
                if (first1.isPresent()) {
                    HashMap<String, Object> jiraParam = new HashMap<>();
                    jiraParam.put("userName", dpvTestFailureRecord.getHeadOfTheInstitute());
                    jiraParam.put("issueType", first1.get().getValue());
                    Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord);
                    map.put("businessId", businessId);
                    map.put("cellNum", childrenList.size() + 1);
                    map.put("cellCode", dpvTestFailureRecord.getCellCode() + "," + childrenList.stream().map(DpvTestFailureRecord::getCellCode).collect(Collectors.joining(",")));
                    map.remove("orderList");
                    map.remove("orderIdList");
                    jiraParam.put("map", map);

                    log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                    JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                    log.info("提交审核返回" + JSON.toJSONString(jiraResp));
                    //提交成功
                    if (jiraResp.getBoolean("result")) {
                        dpvTestFailureRecord.setFaStatus("beStart");
                        dpvTestFailureRecord.setFaResponsibleAssignIssueKey(jiraResp.getString("value"));
                        childrenList.forEach(e -> {
                            e.setFaStatus("beStart");
                            e.setFaResponsibleAssignIssueKey(jiraResp.getString("value"));
                        });
                    } else {
                        String message = jiraResp.getString("message");
                        throw new ServiceException(500, message);
                    }
                }*/
                //发起入库jira流程，创建成功设置库存状态为入库审核中
                Optional<SysDictData> first2 = sysDictDataList.stream().filter(e -> "failureCellInStore".equals(e.getCode())).findFirst();
                // needInStore不为“否”才发起入库流程
                if (!"否".equals(dpvTestFailureRecord.getNeedInStore()) && first2.isPresent()) {
                    HashMap<String, Object> jiraParam = new HashMap<>();
                    jiraParam.put("userName", dpvTestFailureRecord.getCreateAccount());
                    jiraParam.put("issueType", first2.get().getValue());
                    Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord);
                    map.put("businessId", businessId);
                    map.put("summary", dpvTestFailureRecord.getSummary() + "-入库申请");
                    map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord.getFailureCate(), ""));
                    map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord.getLaboratoryId(), ""));
                    map.put("cellNum", childrenList.size() + 1);
                    // 自动触发入库-修改推送到JIRA的电芯编码
                    List<String> cellCodeList = new ArrayList<>();
                    cellCodeList.add("NA".equals(dpvTestFailureRecord.getCellCode()) ? dpvTestFailureRecord.getOrderNo() : dpvTestFailureRecord.getCellCode());
                    if (CollectionUtil.isNotEmpty(childrenList)) {
                        cellCodeList.addAll(childrenList.stream().map(item -> "NA".equals(item.getCellCode()) ? item.getOrderNo() : item.getCellCode()).collect(Collectors.toList()));
                    }
                    map.put("cellCode", String.join("\r\n", cellCodeList));
                    /*
                    map.put("cellCode", childrenList.isEmpty()
                            ? (dpvTestFailureRecord.getCellCode().equals("NA") ? dpvTestFailureRecord.getOrderNo() : dpvTestFailureRecord.getCellCode())
                            : childrenList.stream().map(e -> "NA".equals(e.getCellCode()) ? e.getOrderNo() : e.getCellCode()).collect(Collectors.joining("\r\n")));
                     */
                    map.remove("orderList");
                    map.remove("orderIdList");
                    jiraParam.put("map", map);

                    log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                    JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                    log.info("提交审核返回" + JSON.toJSONString(jiraResp));
                    //提交成功
                    if (jiraResp.getBoolean("result")) {
                        dpvTestFailureRecord.setStockStatus("inStoring");
                        dpvTestFailureRecord.setInStoreAccount(dpvTestFailureRecord.getCreateAccount());
                        dpvTestFailureRecord.setInStoreName(dpvTestFailureRecord.getCreateName());
                        dpvTestFailureRecord.setFailureCellInStoreIssueKey(jiraResp.getString("value"));
                        childrenList.forEach(e -> {
                            e.setStockStatus("inStoring");
                            e.setInStoreAccount(dpvTestFailureRecord.getCreateAccount());
                            e.setInStoreName(dpvTestFailureRecord.getCreateName());
                            e.setFailureCellInStoreIssueKey(jiraResp.getString("value"));
                        });
                    } else {
                        String message = jiraResp.getString("message");
                        throw new ServiceException(500, message);
                    }
                }
                this.updateById(dpvTestFailureRecord);
                if (isUpdateChildren) {
                    this.updateBatchById(childrenList);
                }

                // PBIGLKF-1095 测试失效登记完成后给所有过流程的人员发邮件（登记第三部分“流程审核”中的所有人员，院长除外）
                sendFailureRecordMsg(dpvTestFailureRecord);

            } else if ("failureCellInStore".equals(code)) {
                //入库成功，更新库存状态，入库日期，存放库位
                if ("inStore".equals(dpvTestFailureRecord.getStockStatus())) {
                    return true;//重复提交
                }
                isUpdateChildren = true;
                Date inStoreDate = format.parse(param.getInStoreDate());
                dpvTestFailureRecord.setSampleStatus("inStore");
                dpvTestFailureRecord.setStockStatus("inStore");
                dpvTestFailureRecord.setInStoreDate(inStoreDate);
                dpvTestFailureRecord.setStockLocate(param.getStockLocate());
                childrenList.forEach(e -> {
                    e.setSampleStatus("inStore");
                    e.setStockStatus("inStore");
                    e.setInStoreDate(inStoreDate);
                    e.setStockLocate(param.getStockLocate());
                });
                //入库完成 如已指定FA责任人，发起出库流程 todo
                if (ObjectUtil.isNotEmpty(dpvTestFailureRecord.getFaChargeAccount())) {
                    Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "failureCellOutStore".equals(e.getCode())).findFirst();
                    if (first1.isPresent()){
                        HashMap<String, Object> jiraParam = new HashMap<>();
                        jiraParam.put("userName", dpvTestFailureRecord.getFaChargeAccount());
                        jiraParam.put("issueType", first1.get().getValue());
                        Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord);
                        map.put("businessId", businessId);
                        map.put("summary", dpvTestFailureRecord.getSummary() + "-出库申请");
                        map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord.getFailureCate(), ""));
                        map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord.getLaboratoryId(), ""));
//                        map.put("cellCode", "NA".equals(dpvTestFailureRecord.getCellCode()) ? dpvTestFailureRecord.getOrderNo() : dpvTestFailureRecord.getCellCode());
//                        map.put("cellNum", failureCellOutStoreList.size());
                        map.remove("cellCode");
                        map.remove("orderList");
                        map.remove("orderIdList");
                        jiraParam.put("map", map);
                        log.info("提交测试失效出库流程参数" + JSON.toJSONString(jiraParam));
                        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                        //提交成功
//                        Date outStoreDate = format.parse(param.getOurStoreDate());
                        if (jiraResp.getBoolean("result")) {
                            dpvTestFailureRecord.setFailureCellPreOutStoreIssueKey(jiraResp.getString("value"));
//                            this.updateById(dpvTestFailureRecord);
                        } else {
                            String message = jiraResp.getString("message");
                            throw new ServiceException(500, message);
                        }
                        log.info("提交测试失效出库流程返回" + JSON.toJSONString(jiraResp));
                    }
                }

                this.updateById(dpvTestFailureRecord);
                if (isUpdateChildren) {
                    this.updateBatchById(childrenList);
                }
            } else if ("failureCellOutStore".equals(code)) {
                //查询出库的电芯，同时出库多个样品，多个样品发起同一个分析报告流程
                LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordQueryWrapper = new LambdaQueryWrapper<>();
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFailureCellOutStoreIssueKey, param.getIssueKey());
                List<DpvTestFailureRecord> failureCellOutStoreList = this.list(dpvTestFailureRecordQueryWrapper);
                Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "FAAnalyseReport".equals(e.getCode())).findFirst();
                if (!failureCellOutStoreList.isEmpty()) {
                    DpvTestFailureRecord dpvTestFailureRecord2 = failureCellOutStoreList.get(0);
                    if ("outStore".equals(dpvTestFailureRecord2.getStockStatus())) {
                        return true;//重复提交
                    }
                    //查询是否有正在进行中的分析报告，有的话不发起流程，直接加到该分析报告中
                    Long parentId = dpvTestFailureRecord2.getParentId() == 1L ? dpvTestFailureRecord2.getId() : dpvTestFailureRecord2.getParentId();
                    Date outStoreDate = format.parse(param.getOurStoreDate());
                    LambdaQueryWrapper<DpvTestFailureRecord> faReportQueryWrapper = new LambdaQueryWrapper<>();
                    faReportQueryWrapper.and(e -> e.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId));
                    faReportQueryWrapper.isNotNull(DpvTestFailureRecord::getFaAnalyseReportIssueKey);
                    faReportQueryWrapper.in(DpvTestFailureRecord::getFaStatus, "onGoing", "reviewRejected");
                    List<DpvTestFailureRecord> onGoingFaReportList = this.list(faReportQueryWrapper);
                    if (!onGoingFaReportList.isEmpty()) {
                        failureCellOutStoreList.forEach(e ->{
                            e.setSampleStatus("disassemble");
                            e.setStockStatus("outStore");
                            e.setOutStoreDate(outStoreDate);
                            e.setFaStatus("onGoing");
                            e.setFaAnalyseReportIssueKey(onGoingFaReportList.get(0).getFaAnalyseReportIssueKey());
                        });
                        this.updateBatchById(failureCellOutStoreList);
                        return true;
                    }
                    if (first1.isPresent()){
                        HashMap<String, Object> jiraParam = new HashMap<>();
                        jiraParam.put("userName", dpvTestFailureRecord2.getFaChargeAccount());
                        jiraParam.put("issueType", first1.get().getValue());
                        Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord2);
                        map.put("businessId", businessId);
                        map.put("summary", dpvTestFailureRecord.getSummary() + "-FA分析");
                        map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord.getFailureCate(), ""));
                        map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord.getLaboratoryId(), ""));
                        map.put("cellCode", failureCellOutStoreList.stream().map(e -> "NA".equals(e.getCellCode()) ? e.getOrderNo() : e.getCellCode()).collect(Collectors.joining("\r\n")));
                        map.put("cellNum", failureCellOutStoreList.size());
                        map.remove("orderList");
                        map.remove("orderIdList");
                        jiraParam.put("map", map);
                        log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                        //提交成功
                        if (jiraResp.getBoolean("result")) {
                            failureCellOutStoreList.forEach(e ->{
                                e.setSampleStatus("disassemble");
                                e.setStockStatus("outStore");
                                e.setOutStoreDate(outStoreDate);
                                e.setFaStatus("onGoing");
                                e.setFaAnalyseReportIssueKey(jiraResp.getString("value"));
                            });
                            this.updateBatchById(failureCellOutStoreList);

                            // 修改整体状态 只要有进行中整体状态就为进行中
                            LambdaUpdateWrapper<DpvTestFailureRecord> failureUpdateWrapper = new LambdaUpdateWrapper<>();
                            failureUpdateWrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId);
                            failureUpdateWrapper.set(DpvTestFailureRecord::getOverallFaStatus, "onGoing");
                            this.update(failureUpdateWrapper);

                        } else {
                            String message = jiraResp.getString("message");
                            throw new ServiceException(500, message);
                        }
                        log.info("提交审核返回" + JSON.toJSONString(jiraResp));
                    }
                }



/*                //出库成功，更新库存状态，出库日期
                dpvTestFailureRecord.setStockStatus("outStore");
                dpvTestFailureRecord.setOutStoreDate(format.parse(param.getOurStoreDate()));
                //发起jira流程，责任人上传文件
//                Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "FAAnalyseReport".equals(e.getCode())).findFirst();
                if (first1.isPresent()) {
                    HashMap<String, Object> jiraParam = new HashMap<>();
                    jiraParam.put("userName", dpvTestFailureRecord.getHeadOfTheInstitute());
                    jiraParam.put("issueType", first1.get().getValue());
                    Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord);
                    map.put("businessId", businessId);
                    map.put("cellNum", childrenList.size() + 1);
                    map.remove("orderList");
                    map.remove("orderIdList");
                    jiraParam.put("map", map);
                    log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                    JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                    //提交成功
                    if (jiraResp.getBoolean("result")) {
                        dpvTestFailureRecord.setFaStatus("onGoing");
                        dpvTestFailureRecord.setFaAnalyseReportIssueKey(jiraResp.getString("value"));
                    } else {
                        String message = jiraResp.getString("message");
                        throw new ServiceException(500, message);
                    }
                    log.info("提交审核返回" + JSON.toJSONString(jiraResp));
                    childrenList.forEach(e -> {
                        e.setFaChargeAccount(faResponsibleAccount);
                        e.setFaChargeName(faResponsibleName);
                        e.setFaDeadline(dpvTestFailureRecord.getFaDeadline());
                        Map<String, Object> map1 = BeanUtil.beanToMap(e);
                        map1.put("businessId", e.getId());
                        map1.remove("orderList");
                        map1.remove("orderIdList");
                        jiraParam.put("map", map1);
                        log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                        JSONObject jiraResp1 = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                        //提交成功
                        if (jiraResp1.getBoolean("result")) {
                            e.setFaStatus("onGoing");
                            e.setFaAnalyseReportIssueKey(jiraResp1.getString("value"));
                        } else {
                            String message = jiraResp1.getString("message");
                            throw new ServiceException(500, message);
                        }
                        log.info("提交审核返回" + JSON.toJSONString(jiraResp1));
                    });
                }*/
            } else if ("DPVTestFailureRecord".equals(code)&& isFAResponsibleAssign) {
//                if ("outStore".equals(dpvTestFailureRecord.getStockStatus())) {
//                    return true;//重复提交
//                }
                //设置FA责任人
//                isUpdateChildren = true;
//                String faResponsibleAccount = param.getFaResponsibleAccount();
//                String faResponsibleName = param.getFaResponsibleName();
//                String faDueDate = param.getFaDueDate();
                Date parse = format.parse(faDueDate);

                LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordQueryWrapper = new LambdaQueryWrapper<>();
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFaResponsibleAssignIssueKey, param.getIssueKey());
                List<DpvTestFailureRecord> faResponsibleAssignList = this.list(dpvTestFailureRecordQueryWrapper);
                faResponsibleAssignList.forEach(e->{
                    e.setFaStatus("beStart");
                    e.setReviewStatus("2");
                    e.setOverallFaStatus("beStart");
                    e.setFaChargeAccount(faResponsibleAccount);
                    e.setFaChargeName(faResponsibleName);
                    e.setFaDeadline(parse);
                });
                //已指定FA责任人 如入库完成，发起出库流程 todo
                DpvTestFailureRecord dpvTestFailureRecord2 = faResponsibleAssignList.get(0);
                // 已指定FA责任人：needInStore为“否”直接发起FA流程/否则入库完成才发起出库流程
                if ("否".equals(dpvTestFailureRecord2.getNeedInStore())) {
                    Optional<SysDictData> faReportOptional = sysDictDataList.stream().filter(e -> "FAAnalyseReport".equals(e.getCode())).findFirst();
                    if (faReportOptional.isPresent()){
                        HashMap<String, Object> jiraParam = new HashMap<>();
                        jiraParam.put("userName", faResponsibleAccount);
                        jiraParam.put("issueType", faReportOptional.get().getValue());
                        Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord2);
                        map.put("businessId", businessId);
                        map.put("summary", dpvTestFailureRecord2.getSummary() + "-FA分析");
                        map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord2.getFailureCate(), ""));
                        map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord2.getLaboratoryId(), ""));
                        map.put("cellCode", faResponsibleAssignList.stream().map(e -> "NA".equals(e.getCellCode()) ? e.getOrderNo() : e.getCellCode()).collect(Collectors.joining("\r\n")));
                        map.put("cellNum", faResponsibleAssignList.size());
                        map.remove("orderList");
                        map.remove("orderIdList");
                        jiraParam.put("map", map);
                        log.info("提交审核信息" + JSON.toJSONString(jiraParam));
                        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                        //提交成功
                        if (jiraResp.getBoolean("result")) {
                            faResponsibleAssignList.forEach(e ->{
                                e.setFaStatus("onGoing");
                                e.setOverallFaStatus("onGoing");
                                e.setFaAnalyseReportIssueKey(jiraResp.getString("value"));
                            });
                        } else {
                            String message = jiraResp.getString("message");
                            throw new ServiceException(500, message);
                        }
                        log.info("提交审核返回" + JSON.toJSONString(jiraResp));
                    }
                } else if (ObjectUtil.isNotEmpty(dpvTestFailureRecord2.getInStoreDate())) {
                    Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "failureCellOutStore".equals(e.getCode())).findFirst();
                    if (first1.isPresent()){
                        HashMap<String, Object> jiraParam = new HashMap<>();
                        jiraParam.put("userName", dpvTestFailureRecord2.getFaChargeAccount());
                        jiraParam.put("issueType", first1.get().getValue());
                        Map<String, Object> map = BeanUtil.beanToMap(dpvTestFailureRecord2);
                        map.put("businessId", businessId);
                        map.put("summary", dpvTestFailureRecord2.getSummary() + "-出库申请");
                        map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord2.getFailureCate(), ""));
                        map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord2.getLaboratoryId(), ""));
//                        map.put("cellCode", faResponsibleAssignList.stream().map(e -> "NA".equals(e.getCellCode()) ? e.getOrderNo() : e.getCellCode()).collect(Collectors.joining("\r\n")));
//                        map.put("cellNum", faResponsibleAssignList.size());
                        map.remove("cellCode");
                        map.remove("orderList");
                        map.remove("orderIdList");
                        jiraParam.put("map", map);
                        log.info("提交测试失效出库流程参数" + JSON.toJSONString(jiraParam));
                        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                        //提交成功
//                        Date outStoreDate = format.parse(param.getOurStoreDate());
                        if (jiraResp.getBoolean("result")) {
                            dpvTestFailureRecord.setFailureCellPreOutStoreIssueKey(jiraResp.getString("value"));
                            this.updateById(dpvTestFailureRecord);
                            /*
                            faResponsibleAssignList
                                    .forEach(e -> {
                                        e.setStockStatus("outStoring");
                                        e.setFailureCellPreOutStoreIssueKey(jiraResp.getString("value"));
                                    });
                             */
                        } else {
                            String message = jiraResp.getString("message");
                            throw new ServiceException(500, message);
                        }
                        log.info("提交测试失效出库流程返回" + JSON.toJSONString(jiraResp));
                    }
                }
                this.updateBatchById(faResponsibleAssignList);

            } else if ("FAAnalyseReport".equals(code)) {
                //分析报告审核通过 接收jira上传的文件
                String faBreakReportUrl = param.getFaBreakReportUrl();
                String faBreakReportName = param.getFaBreakReportName();
                String faAnalysisReportUrl = param.getFaAnalysisReportUrl();
                String faAnalysisReportName = param.getFaAnalysisReportName();
                LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordQueryWrapper = new LambdaQueryWrapper<>();
                dpvTestFailureRecordQueryWrapper.or().eq(DpvTestFailureRecord::getFaAnalyseReportIssueKey, param.getIssueKey());
                List<DpvTestFailureRecord> faAnalyseReportList = this.list(dpvTestFailureRecordQueryWrapper);
                DpvTestFailureRecord dpvTestFailureRecord2 = faAnalyseReportList.get(0);
                if ("finish".equals(dpvTestFailureRecord2.getFaStatus())) {
                    return true;//重复提交
                }
                if (!faAnalyseReportList.isEmpty()) {

                    String allFaBreakReport = dpvTestFailureRecord2.getAllFaBreakReport();
                    List<JSONObject> breakList = StrUtil.isNotEmpty(allFaBreakReport)
                            ? JSONObject.parseArray(allFaBreakReport, JSONObject.class)
                            : new ArrayList<>();
                    String allFaAnalysisReport = dpvTestFailureRecord2.getAllFaAnalysisReport();
                    List<JSONObject> analysisList = StrUtil.isNotEmpty(allFaAnalysisReport)
                            ? JSONObject.parseArray(allFaAnalysisReport, JSONObject.class)
                            : new ArrayList<>();
                    // 如果dpvTestFailureRecord2已经有分析报告文件，则是从PBI传到JIRA，无需从JIRA下载
                    if (StrUtil.isEmpty(dpvTestFailureRecord2.getFaAnalysisReportId())) {
                        // 拆解报告和分析报告规范命名
                        // 《拆解报告》命名规则：产品名称-样品阶段-FA拆解报告-A版-20240430
                        // 《分析报告》命名规则：产品名称-样品阶段-FA分析报告-A版-20240430
                        // 根据历史文件数量确定顺序编号
                        String breakLetterCode = numberToLetterCode(breakList.size());
                        String analysisLetterCode = numberToLetterCode(analysisList.size());
                        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
                        // 拼接文件名
                        if (StrUtil.isNotEmpty(faBreakReportName) && faBreakReportName.contains(".")) {
                            String breakSuffix = faBreakReportName.split("\\.")[faBreakReportName.split("\\.").length - 1];
                            faBreakReportName = dpvTestFailureRecord2.getProductName() + "-" + dpvTestFailureRecord2.getTestSampleStage() + "-FA拆解报告-" + breakLetterCode + "版-" + dateStr + "." + breakSuffix;
                        }
                        if (StrUtil.isNotEmpty(faAnalysisReportName) && faAnalysisReportName.contains(".")) {
                            String analysisSuffix = faAnalysisReportName.split("\\.")[faAnalysisReportName.split("\\.").length - 1];
                            faAnalysisReportName = dpvTestFailureRecord2.getProductName() + "-" + dpvTestFailureRecord2.getTestSampleStage() + "-FA分析报告-" + analysisLetterCode + "版-" + dateStr + "." + analysisSuffix;
                        }

                        //根据附件ID到jira下载文件到PBI
                        JSONObject resp = new JSONObject();
                        try {
                            resp = authService.getToken(param.getUserName());
                        } catch (Exception e) {
                            resp = new JSONObject();
                            //不抛错
                            log.error("JIRA登录失败"+ JSON.toJSONString(param.getUserName()));
                            //throw new AuthException(AuthExceptionEnum.REQUEST_JIRA_TOKEN_FAIL);
                        }
                        String token = resp.getString("token");
                        Long faBreakReportId = downFileAndUpload(faBreakReportUrl, faBreakReportName, token, FileConfig.FA_BREAK_REPORT);
                        try {
                            resp = authService.getToken(param.getUserName());
                            token = resp.getString("token");
                        } catch (Exception e) {
                            resp = new JSONObject();
                            //不抛错
                            log.error("JIRA登录失败"+ JSON.toJSONString(param.getUserName()));
                            //throw new AuthException(AuthExceptionEnum.REQUEST_JIRA_TOKEN_FAIL);
                        }
                        Long faAnalysisReportId = downFileAndUpload(faAnalysisReportUrl, faAnalysisReportName, token, FileConfig.FA_ANALYSIS_REPORT);

                        for (DpvTestFailureRecord record : faAnalyseReportList) {
                            record.setFaBreakReportId(faBreakReportId.toString());
                            record.setFaBreakReportName(faBreakReportName);
                            record.setFaAnalysisReportId(faAnalysisReportId.toString());
                            record.setFaAnalysisReportName(faAnalysisReportName);
                        }

                        // 增加拆解报告和分析报告
                        JSONObject breakReportObj = new JSONObject();
                        breakReportObj.put("fileId", faBreakReportId);
                        breakReportObj.put("fileName", faBreakReportName);
                        breakList.add(breakReportObj);
                        JSONObject analysisReportObj = new JSONObject();
                        analysisReportObj.put("fileId", faAnalysisReportId);
                        analysisReportObj.put("fileName", faAnalysisReportName);
                        analysisList.add(analysisReportObj);
                    }

                    for (DpvTestFailureRecord record : faAnalyseReportList) {
                        record.setFaStatus("finish");
                        record.setImproveEffectEvaluation(param.getImproveEffectEvaluation());
                    }
                    this.updateBatchById(faAnalyseReportList);

                    // 查看同一失效登记里面是否有进行中，没有才改为完成
                    LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
                    Long parentId = dpvTestFailureRecord2.getParentId() == 1L ? dpvTestFailureRecord2.getId() : dpvTestFailureRecord2.getParentId();
                    queryWrapper.and(wrapper -> wrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId));
                    queryWrapper.in(DpvTestFailureRecord::getFaStatus, Arrays.asList("onGoing", "inReview", "reviewRejected"));
                    int count = this.count(queryWrapper);
                    String overallFaStatus = count > 0 ? "onGoing" : "finish";
                    // 修改整体状态和全部报告字段
                    LambdaUpdateWrapper<DpvTestFailureRecord> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId);
                    updateWrapper.set(DpvTestFailureRecord::getAllFaBreakReport, JSONObject.toJSONString(breakList));
                    updateWrapper.set(DpvTestFailureRecord::getAllFaAnalysisReport, JSONObject.toJSONString(analysisList));
                    updateWrapper.set(DpvTestFailureRecord::getOverallFaStatus, overallFaStatus);
                    this.update(updateWrapper);

                }

            }

        }

        return true;
    }

    private Long downFileAndUpload(String url, String fileName, String token, String bucket) throws Exception {
        FileInputStream inputStream = null;
        String filePath = fileOperator.newEmptyFile(FileConfig.FA_REPORT_TMP, fileName);
        File file = new File(filePath);
        DownloadUtil.downloadJiraFile(url, token, file);
        inputStream = new FileInputStream(file);
        MultipartFile faAnalysisReportMultipartFile = new MockMultipartFile(file.getName(), file.getName(), null, inputStream);
        return minioService.uploadFile(null, faAnalysisReportMultipartFile, null, null);
    }

    @Deprecated
    @Override
    public void failureCellInStore(DpvTestFailureRecord param) {
        DpvTestFailureRecord dpvTestFailureRecord = this.getById(param);
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        //调用jira发起流程
        //信息推送JIRA生成流程审核
        HashMap<String, Object> jiraParam = new HashMap<>();
        jiraParam.put("userName", sysLoginUser.getAccount());
        List<SysDictData> sysDictDataList = this.getDictByCode("JIRA_IssueTypeId");
        Optional<SysDictData> first = sysDictDataList.stream().filter(e -> "failureCellInStore".equals(e.getCode())).findFirst();
        first.ifPresent(sysDictData -> jiraParam.put("issueType", sysDictData.getValue()));
        Map<String, Object> map = BeanUtil.beanToMap(param);
        map.put("businessId", param.getId());
        map.put("summary", dpvTestFailureRecord.getSummary() + "-入库申请");
        map.put("failureCate", failureCateMap.getOrDefault(dpvTestFailureRecord.getFailureCate(), ""));
        map.put("laboratoryId", laboratoryIdMap.getOrDefault(dpvTestFailureRecord.getLaboratoryId(), ""));
//        map.put("cellNum", childrenList.size() + 1);
        jiraParam.put("map", map);

        log.info("测试失效入库提交审核信息" + JSON.toJSONString(jiraParam));
        JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
        log.info("测试失效入库提交审核返回" + JSON.toJSONString(jiraResp));

        //提交成功
        if (jiraResp.getBoolean("result")) {
            DpvTestFailureRecord inStoring = DpvTestFailureRecord.builder()
                    .id(dpvTestFailureRecord.getId())
                    .inStoreAccount(sysLoginUser.getAccount())
                    .inStoreName(sysLoginUser.getName())
                    .stockStatus("inStoring").build();
            inStoring.setFailureCellInStoreIssueKey(jiraResp.getString("value"));
            this.updateById(inStoring);
        } else {
            param.setStockStatus("preInStore");
            throw new ServiceException(500, "提交审批失败。" + jiraResp.getString("message"));
        }
    }

    @Override
    public void failureCellOutStore(String token, DpvTestFailureRecord param) {
        List<Long> idList = param.getIdList();
//        DpvTestFailureRecord dpvTestFailureRecord = this.getById(param);
        List<DpvTestFailureRecord> outStoreList = new ArrayList<>();
        if (idList == null||idList.isEmpty()) {
//            List<DpvTestFailureRecord> children = param.getChildren();
//            if (children == null || children.size() == 0) {
                throw new ServiceException(500, "请选择样品进行出库");
//            }
//            LambdaQueryWrapper<DpvTestFailureRecord> wrapper = new LambdaQueryWrapper<>();
//            wrapper.in(DpvTestFailureRecord::getId, idList);
//            outStoreList = this.list(wrapper);
//            this.getByBatchId(param);
        }else {
//            outStoreList.add(dpvTestFailureRecord);
            LambdaQueryWrapper<DpvTestFailureRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(DpvTestFailureRecord::getId, idList);
            outStoreList = this.list(wrapper);
        }
        Optional<DpvTestFailureRecord> parentDpvTestFailureRecordOptional = outStoreList.stream().filter(e -> e.getParentId() == 1L).findFirst();
        DpvTestFailureRecord parentDpvTestFailureRecord = null;
        if (parentDpvTestFailureRecordOptional.isPresent()) {
            parentDpvTestFailureRecord = parentDpvTestFailureRecordOptional.get();
        } else {
            DpvTestFailureRecord dpvTestFailureRecord = outStoreList.get(0);
            parentDpvTestFailureRecord = this.getById(dpvTestFailureRecord.getParentId());
        }
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUserWithoutException();
        String failureCellPreOutStoreIssueKey = parentDpvTestFailureRecord.getFailureCellPreOutStoreIssueKey();
        String cellCodes = outStoreList.stream().map( e -> "NA".equals(e.getCellCode()) ? e.getOrderNo() : e.getCellCode()).collect(Collectors.joining("\n"));

        // 若同一失效登记有电芯FailureCellOutStoreIssueKey不为空，说明不是首次出库，则另起新的出库流程
        Long parentId = parentDpvTestFailureRecord.getId();
        LambdaQueryWrapper<DpvTestFailureRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq(DpvTestFailureRecord::getId, parentId).or().eq(DpvTestFailureRecord::getParentId, parentId));
        queryWrapper.isNotNull(DpvTestFailureRecord::getFailureCellOutStoreIssueKey);
        int count = this.count(queryWrapper);
        if (count > 0) {
            List<SysDictData> sysDictDataList = this.getDictByCode("JIRA_IssueTypeId");
            Optional<SysDictData> first1 = sysDictDataList.stream().filter(e -> "failureCellOutStore".equals(e.getCode())).findFirst();
            if (first1.isPresent()){
                HashMap<String, Object> jiraParam = new HashMap<>();
                jiraParam.put("userName", parentDpvTestFailureRecord.getFaChargeAccount());
                jiraParam.put("issueType", first1.get().getValue());
                Map<String, Object> map = BeanUtil.beanToMap(parentDpvTestFailureRecord);
                map.put("businessId", parentDpvTestFailureRecord.getId());
                map.put("summary", parentDpvTestFailureRecord.getSummary() + "-出库申请");
                map.put("failureAnalysisContent", param.getFailureAnalysisContent());
                map.put("failureCate", failureCateMap.getOrDefault(parentDpvTestFailureRecord.getFailureCate(), ""));
                map.put("laboratoryId", laboratoryIdMap.getOrDefault(parentDpvTestFailureRecord.getLaboratoryId(), ""));
                map.put("cellCode", cellCodes);
                map.put("cellNum", outStoreList.size());
                map.remove("orderList");
                map.remove("orderIdList");
                map.remove("causeAnalysis");
                map.remove("tempMeasures");
                map.remove("longTermMeasures");
                map.remove("resultVerification");
                jiraParam.put("map", map);
                log.info("提交测试失效出库流程参数" + JSON.toJSONString(jiraParam));
                JSONObject jiraResp = Utils.doPostAndToken(JiraApiParams.testFailureCreateCheck, JiraApiParams.Token, jiraParam);
                log.info("提交测试失效出库流程返回" + JSON.toJSONString(jiraResp));
                if (!jiraResp.getBoolean("result")) {
                    throw new ServiceException(500, "发起测试失效出库流程失败：" + jiraResp.getString("message"));
                }

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                // 等待1秒后提交JIRA，跳过“出库单新选择”节点
                String issueKey = jiraResp.getString("value");
                Map<String, String> outStoreMap = new HashMap<>();
                outStoreMap.put("cellCode", cellCodes);
                outStoreMap.put("failureAnalysisContent", param.getFailureAnalysisContent());
                Map<String, Object> params = new HashMap<String, Object>(4) {
                    {
                        put("issueKey", issueKey);
                        put("userName", sysLoginUser.getAccount());
                        put("transitionId", "31");
                        put("map", outStoreMap);
                    }
                };
                log.info("测试失效出库提交审核信息" + JSON.toJSONString(params));
                JSONObject resp = Utils.doPostAndToken(JiraApiParams.StageTransition, token, params);
                log.info("测试失效出库提交审核返回" + JSON.toJSONString(resp));
                if (resp.getBoolean("result")) {
                    outStoreList.forEach(e -> {
                        e.setOutStoreAccount(sysLoginUser.getAccount());
                        e.setOutStoreName(sysLoginUser.getName());
                        e.setStockStatus("outStoring");
                        e.setFailureCellOutStoreIssueKey(issueKey);
                        e.setFailureAnalysisContent(param.getFailureAnalysisContent());
                    });
                    this.updateBatchById(outStoreList);
                } else {
                    throw new ServiceException(500, "发起测试失效出库流程失败：" + resp.getString("message"));
                }
            }

            return;
        }

        Map<String, String> map = new HashMap<>();
        map.put("cellCode", cellCodes);
        map.put("failureAnalysisContent", param.getFailureAnalysisContent());
        Map<String, Object> params = new HashMap<String, Object>(4) {
            {
                put("issueKey", failureCellPreOutStoreIssueKey);
                put("userName", sysLoginUser.getAccount());
                put("transitionId", "31");
                put("map", map);
            }
        };
        log.info("测试失效出库提交审核信息" + JSON.toJSONString(params));
        JSONObject resp = Utils.doPostAndToken(JiraApiParams.StageTransition, token, params);
        log.info("测试失效出库提交审核返回" + JSON.toJSONString(resp));
        if (resp.getBoolean("result")) {
            outStoreList.forEach(e -> {
                e.setOutStoreAccount(sysLoginUser.getAccount());
                e.setOutStoreName(sysLoginUser.getName());
                e.setStockStatus("outStoring");
                e.setFailureCellOutStoreIssueKey(failureCellPreOutStoreIssueKey);
                e.setFailureAnalysisContent(param.getFailureAnalysisContent());
            });
            this.updateBatchById(outStoreList);
            /*
            parentDpvTestFailureRecord.setFailureCellPreOutStoreIssueKey(null);
            this.updateById(parentDpvTestFailureRecord);
             */
        } else {
            throw new ServiceException(500, "电芯推送JIRA失败：" + resp.getString("message"));
        }

    }

    @Override
    public void exportOcvTemplate(HttpServletResponse response, DpvTestFailureRecord param) throws IOException {
        InputStream inStream = null;
        // 需要导出的数据
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 生成表头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
            String fileName = URLEncoder.encode("失效电芯OCV填写模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            inStream = new FileInputStream(fileOperator.newEmptyFile(FileConfig.FA_REPORT_TMP, "失效电芯OCV填写模板.xlsx"));
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(inStream).build();
            // 如果不用模板的方式导出的话，是doWrite
            List<List<String>> header = generateTemplateHeader();
//            List<List<String>> header = new ArrayList<>();
            WriteSheet writeSheet = EasyExcel.writerSheet("OCV").build();
            writeSheet.setHead(header);
            // 设置表头列宽
            Map<Integer, Integer> columnWidthMap = setCoulmnWidthByHeaders(header);
            writeSheet.setColumnWidthMap(columnWidthMap);
            List<List<Object>> body = generateTemplateData(header);
//            List<List<Object>> body = new ArrayList<>();
            excelWriter.write(body, writeSheet);
            WriteContext writeContext = excelWriter.writeContext();
            WriteSheetHolder writeSheetHolder = writeContext.writeSheetHolder();
            Sheet sheet = writeSheetHolder.getSheet();
            // 强制让该Sheet执行公式，获取当前时间
            sheet.setForceFormulaRecalculation(true);
            for (int i = 3; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                Cell cell = row.createCell(100);
                int formulaIndex = i + 1;
                String formulaValue1 = "H" + formulaIndex;
                String formulaValue2 = "CW" + formulaIndex;
                String formula = String.format("IF(%s=\"\",\"\",IF(%s<>\"\",%s,TEXT(NOW(), \"yyyy-mm-dd hh:mm:ss\")))",formulaValue1,formulaValue2,formulaValue2);
                cell.setCellFormula(formula);
            }
            sheet.setColumnHidden(100,true);
            // 设置表头样式
            setTableHeaderStyle(sheet);
            // 设置表格内容样式
            setTableContentStyle(sheet, header.size());
            // 添加备注
            setTableRemark(sheet);
            excelWriter.finish();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != inStream) {
                inStream.close();
            }
        }
    }

    /**
     *生成表头
     */
    public List<List<String>> generateTemplateHeader() {
        List<List<String>> header = new ArrayList<>();
        header.add(new ArrayList<>(Collections.nCopies(1, "序号")));
        header.add(new ArrayList<>(Collections.nCopies(1, "产品名称")));
        header.add(new ArrayList<>(Collections.nCopies(1, "电芯编码")));
        // 中检后
        header.add(new ArrayList<>(Collections.nCopies(1, "内阻/Ω")));
        header.add(new ArrayList<>(Collections.nCopies(1, "电压/V")));
        return header;
    }

    public Map<Integer, Integer> setCoulmnWidthByHeaders(List<List<String>> header) {
        Map<Integer, Integer> columnWidthMap = new HashMap<>();
        columnWidthMap.put(0, 2000); // 序号
        columnWidthMap.put(1, 2500); // 产品名称
        columnWidthMap.put(2, 4000); // 电芯编码
        columnWidthMap.put(3, 3000); // 内阻
        columnWidthMap.put(4, 2000); // 电压
        return columnWidthMap;
    }

    public List<List<Object>> generateTemplateData(List<List<String>> headers) {
        //获取已存在的OCV数据
        List<String> headerList = new ArrayList<>();
        for (List<String> head : headers) {
            headerList.add(head.get(0));
        }
        // 去除掉已经写入值的部分
//        headerList = headerList.subList(3, headerList.size());
        List<List<String>> result = new ArrayList<>();
        TsetFailureQueryParam tsetFailureQueryParam = TsetFailureQueryParam.builder().stockStatusList(new ArrayList<>(Arrays.asList("inStore","outStoring"))).build();
        List<DpvTestFailureRecord> dpvTestFailureRecordList = this.list(tsetFailureQueryParam);
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        LambdaQueryWrapper<FailureCellOcvRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FailureCellOcvRecord::getTestFailureId, dpvTestFailureRecordList.stream().map(DpvTestFailureRecord::getId).collect(Collectors.toList()));
        queryWrapper.gt(FailureCellOcvRecord::getRecordDate, todayStart.getTime());
        queryWrapper.lt(FailureCellOcvRecord::getRecordDate, todayEnd.getTime());
        List<FailureCellOcvRecord> failureCellOcvRecordList = iFailureCellOcvRecordService.list(queryWrapper);

        for (int i = 0;i < dpvTestFailureRecordList.size();i++) {
            List<String> dataList = new ArrayList<>();
            int number = i + 1;
            DpvTestFailureRecord dpvTestFailureRecord = dpvTestFailureRecordList.get(i);
            Optional<FailureCellOcvRecord> first = failureCellOcvRecordList.stream().filter(e -> e.getTestFailureId().equals(dpvTestFailureRecord.getId())).findFirst();

            dataList.add(String.valueOf(number));//序号
            dataList.add(dpvTestFailureRecord.getProductName());//产品名称
            dataList.add(dpvTestFailureRecord.getCellCode());//电芯编码
            if (first.isPresent()) {
                //赋值已存在的OCV数据
                FailureCellOcvRecord failureCellOcvRecord = first.get();
                dataList.add(transInnerresAndVoltage(failureCellOcvRecord.getInsideRes()));
                dataList.add(transInnerresAndVoltage(failureCellOcvRecord.getVoltage()));
            }
            result.add(dataList);
        }
        return transTempStringToNumeric(result);
    }

    // 导出技师模板时,电压要除以1000,因为导入时乘以1000了
    public String transInnerresAndVoltage(String string) {
        String objectResult;
        if (StringUtils.isNotEmpty(string) && string.matches("[+-]?\\d+\\.?\\d*")) {
            BigDecimal value = new BigDecimal(string);
            objectResult = value.divide(new BigDecimal(1000)).setScale(3, RoundingMode.HALF_UP).toPlainString();;
        } else {
            objectResult = string;
        }
        return objectResult;
    }

    public List<List<Object>> transTempStringToNumeric(List<List<String>> stringResult) {
        List<List<Object>> dataList = new ArrayList<>();
        for (List<String> strings : stringResult) {
            List<Object> datas = new ArrayList<>();
            for (int i = 0; i < strings.size(); i++) {
                String data = strings.get(i);
                if (StringUtils.isNotEmpty(data) && data.matches("[+-]?\\d+\\.?\\d*")) {
                    datas.add(new BigDecimal(data));
                } else {
                    datas.add(data);
                }
            }
            dataList.add(datas);
        }
        return dataList;
    }

    public void setTableHeaderStyle(Sheet sheet) {
//        for (int i = 0; i <= 0; i++) {
            Row row = sheet.getRow(0);
            for (int j = 0; j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
                // 设置表头字体样式
                Font font = cell.getSheet().getWorkbook().createFont();
                font.setFontName("思源黑体 CN Normal");
                font.setFontHeightInPoints((short) 11);
                font.setBold(true);
                headerStyle.setFont(font);
                // 设置表头背景颜色
                if (j <= 2) {
                    headerStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());
                    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                } else {
                    headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                    headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                }
                // 设置字体居中
                headerStyle.setAlignment(HorizontalAlignment.CENTER);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                // 设置边框样式
                headerStyle.setBorderTop(BorderStyle.THIN);
                headerStyle.setBorderBottom(BorderStyle.THIN);
                headerStyle.setBorderLeft(BorderStyle.THIN);
                headerStyle.setBorderRight(BorderStyle.THIN);
                // 设置边框颜色
                headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                cell.setCellStyle(headerStyle);
            }
//        }
    }

    public void setTableContentStyle(Sheet sheet,int headerSize) {
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            for (int j = 0; j < headerSize; j++) {
                Cell cell = row.getCell(j);
                if (cell == null) {
                    cell = row.createCell(j);
                }
                CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
                // 设置表头字体样式
                Font font = sheet.getWorkbook().createFont();
                font.setFontName("思源黑体 CN Normal");
                font.setFontHeightInPoints((short) 10);
                headerStyle.setFont(font);
                // 设置字体居中
                headerStyle.setAlignment(HorizontalAlignment.CENTER);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                // 设置边框样式
                headerStyle.setBorderTop(BorderStyle.THIN);
                headerStyle.setBorderBottom(BorderStyle.THIN);
                headerStyle.setBorderLeft(BorderStyle.THIN);
                headerStyle.setBorderRight(BorderStyle.THIN);
                // 设置边框颜色
                headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
                headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                cell.setCellStyle(headerStyle);
            }
        }
    }

    public void setTableRemark(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();
        int rowOfRemark = lastRowNum + 2;
        Row remarkRow = sheet.createRow(rowOfRemark);
        Cell remarkCell = remarkRow.createCell(0);
        remarkCell.setCellValue("备注：");
        int rowOfRemarkContent1 = lastRowNum + 3;
        int rowOfRemarkContent2 = lastRowNum + 4;
        Row remarkContentRow1 = sheet.createRow(rowOfRemarkContent1);
        Row remarkContentRow2 = sheet.createRow(rowOfRemarkContent2);
        Cell remarkContentCell11 = remarkContentRow1.createCell(0);
        Cell remarkContentCell12 = remarkContentRow1.createCell(1);
        Cell remarkContentCell21 = remarkContentRow2.createCell(0);
        Cell remarkContentCell22 = remarkContentRow2.createCell(1);
        CellStyle remarkStyle1 = sheet.getWorkbook().createCellStyle();
        CellStyle remarkStyle2 = sheet.getWorkbook().createCellStyle();
        remarkStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        remarkStyle1.setFillForegroundColor(IndexedColors.TAN.getIndex());
        remarkContentCell11.setCellStyle(remarkStyle1);
        remarkContentCell12.setCellValue("自动从系统中带出来的数据");
        remarkStyle2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        remarkStyle2.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        remarkContentCell21.setCellStyle(remarkStyle2);
        remarkContentCell22.setCellValue("表示需填写的数据");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData importOcvTemplate(MultipartFile file, Long ordTaskId, String middleCheckStage) throws IOException {
//        TestProgressDetail updateTarget = testProgressDetailService.getById(ordTaskId);
        // 读取Excel文件
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        // 读取第一个Sheet页
        Sheet sheet = workbook.getSheetAt(0);

        if (!checkImportTemplateHeader(sheet)) {
            return ResponseData.error("导入模板与实际填写数据不匹配！");
        }

        List<String> headerNameList = new ArrayList<>();
        List<List<Object>> valueList = new ArrayList<>();
        List<String> recordTimeList = new ArrayList<>();
        Row headerNameRow = sheet.getRow(0);
        // 获取表头名称
        for (Cell cell : headerNameRow) {
            headerNameList.add(String.valueOf(getCellValue(cell)));
        }
        // 去除从系统中带出来的数据
//        headerNameList = headerNameList.subList(3, headerNameList.size());

        LinkedHashSet<String> set = new LinkedHashSet<>(headerNameList);
        List<String> middleHeaderNameList = new ArrayList<>(set);
        List<String> finalHeaderNameList = new ArrayList<>();
        List<String> finalHeaderFieldList = new ArrayList<>();
        List<String> baseNameList = Arrays.asList("序号", "产品名称", "电芯编码", "内阻", "电压");
        for (String headerName : middleHeaderNameList) {
            if (baseNameList.stream().anyMatch(headerName::contains)) {
                finalHeaderNameList.add(headerName);
                /*
                 *         headerNameTransToFieldsMap.put("内阻/Ω","beforeInnerres");
                 *         headerNameTransToFieldsMap.put("电压/V","beforeVoltage");
                 */
                finalHeaderFieldList.add(headerNameTransToFieldsMap.get(headerName));
            }
        }
        // 有ordTask就根据工程师填写的尺寸信息生成待录入数据列，否则生成默认的录入数据列
//        if (StringUtils.equals(updateTarget.getHeight(), "1")) {
//            TestProgress testProgress = testProgressService.getById(updateTarget.getProgressId());
//            LambdaQueryWrapper<TLimsTemGradient> temGradientWrapper = new LambdaQueryWrapper<>();
//            temGradientWrapper.inSql(TLimsTemGradient::getOrdtaskconditionid,
//                    "SELECT ID FROM T_LIMS_ORDTASK_CONDITION WHERE DATATYPE = 'sizeInformation' AND ORDTASKID = " + testProgress.getOrdtaskid());
//            temGradientWrapper.orderByAsc(TLimsTemGradient::getOrderno);
//            List<TLimsTemGradient> temGradientlist = tLimsTemGradientService.list(temGradientWrapper);
//            if (temGradientlist.size() > 0) {
//                generateImpSizeInfoHeader(finalHeaderNameList, finalHeaderFieldList, temGradientlist, testProgress.getSampleType());
//            } else {
//                // 生成默认的录入数据列
//                getDefaultImpSizeInfoHeaderByType(finalHeaderNameList, finalHeaderFieldList, testProgress.getSampleType());
//            }
//        }
        if (finalHeaderNameList.size() != finalHeaderFieldList.size()) {
            return ResponseData.error("导入数据异常, 请联系管理员！");
        }

        // 获取导入的值
        // 遍历每一行,减去4的原因是导入excel的最后4行不是用户填写的数据
        int rowSize = sheet.getLastRowNum() - 4;
        for (int i = 1; i <= rowSize; i++) {
            Row row = sheet.getRow(i);
            List<Object> cellValueList = new ArrayList<>();
            // 遍历每个单元格
            int lastCellNum = row.getLastCellNum();
            for (int j = 0; j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cellValueList.add(getCellValue(cell));
                // POI导入Excel的数据时，直接获取表中的值，如果表中单元格的值时由公式计算得出的话，获取到的是公式,所以需要对获取的单元格的值进行处理
                if (j == lastCellNum - 1) {
                    recordTimeList.add(String.valueOf(getCellValue(cell)));
                }
            }
//            valueList.add(cellValueList.subList(3, cellValueList.size()));
            valueList.add(cellValueList);
        }
        // 将表头和表值合并成一个map集合
        List<Map<String, Object>> importDataList = mergeHeaderAndValuesToMaps(finalHeaderNameList, finalHeaderFieldList, valueList, recordTimeList);
        List<String> cellCodeList = importDataList.stream().map(e -> e.get("cellCode") == null ? "" : (String) e.get("cellCode")).collect(Collectors.toList());
        LambdaQueryWrapper<DpvTestFailureRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(DpvTestFailureRecord::getCellCode, cellCodeList);
        List<DpvTestFailureRecord> dpvTestFailureRecordList = list(lambdaQueryWrapper);

        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);

        LambdaQueryWrapper<FailureCellOcvRecord> failureCellOcvRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        failureCellOcvRecordLambdaQueryWrapper.in(FailureCellOcvRecord::getCellCode, cellCodeList);
        failureCellOcvRecordLambdaQueryWrapper.gt(FailureCellOcvRecord::getRecordDate, todayStart.getTime());
        failureCellOcvRecordLambdaQueryWrapper.lt(FailureCellOcvRecord::getRecordDate, todayEnd.getTime());
        List<FailureCellOcvRecord> failureCellOcvRecordList = iFailureCellOcvRecordService.list(failureCellOcvRecordLambdaQueryWrapper);

        //已存在OCV更新，不存在创建
        for (DpvTestFailureRecord dpvTestFailureRecord : dpvTestFailureRecordList) {
            Map<String, Object> recordMap = importDataList.stream().filter(e -> dpvTestFailureRecord.getCellCode().equals(e.get("cellCode"))).findFirst().orElseGet(null);
            Optional<FailureCellOcvRecord> first = failureCellOcvRecordList.stream().filter(e -> dpvTestFailureRecord.getId().equals(e.getTestFailureId())).findFirst();

            String innerRes = new BigDecimal((String) recordMap.get("innerRes")).multiply(BigDecimal.valueOf(1000)).setScale(2, RoundingMode.HALF_UP).toPlainString();
            String voltage = new BigDecimal((String) recordMap.get("voltage")).multiply(BigDecimal.valueOf(1000)).setScale(1, RoundingMode.HALF_UP).toPlainString();

            if (first.isPresent()) {
                FailureCellOcvRecord failureCellOcvRecord = first.get();
                failureCellOcvRecord.setInsideRes(innerRes);
                failureCellOcvRecord.setVoltage(voltage);
                failureCellOcvRecord.setRecordDate(new Date());
                iFailureCellOcvRecordService.updateById(failureCellOcvRecord);
            } else {
                FailureCellOcvRecord failureCellOcvRecord = FailureCellOcvRecord.builder()
                        .productId(dpvTestFailureRecord.getProductId())
                        .productName(dpvTestFailureRecord.getProductName())
                        .cellCode(dpvTestFailureRecord.getCellCode())
                        .recordDate(new Date())
                        .insideRes(innerRes)
                        .voltage(voltage)
                        .testFailureId(dpvTestFailureRecord.getId()).build();
                iFailureCellOcvRecordService.save(failureCellOcvRecord);
            }

        }


        // 获取详细测试数据
//        String jsonString = updateTarget.getLifeTestRecord();
//        ObjectMapper objectMapper = new ObjectMapper();
//        try {
//            // 将 JSON 字符串转换为 List
//            if (jsonString != null) {
//                List<Map<String, Object>> originalDataList = objectMapper.readValue(jsonString, new TypeReference<List<Map<String, Object>>>() {});
//                updateLifeTestRecordDataByImport(originalDataList, importDataList);
//                String updateJsonStringData = objectMapper.writeValueAsString(originalDataList);
//                TestProgressDetail finalUpdateTarget = new TestProgressDetail();
//                finalUpdateTarget.setId(updateTarget.getId());
//                finalUpdateTarget.setLifeTestRecord(updateJsonStringData);
//                testProgressDetailService.updateById(finalUpdateTarget);
//            }
//        } catch (JsonProcessingException e) {
//            e.printStackTrace();
//        }
        return new SuccessResponseData();
    }

    public boolean checkImportTemplateHeader(Sheet sheet) throws IOException {
        List<List<String>> sourceHeaderList = generateTemplateHeader();
        List<List<String>> headerList = new ArrayList<>();
        for (int i = 0; i < sourceHeaderList.size(); i++) {
            List<String> rowStringList = new ArrayList<>();
            Row row1 = sheet.getRow(0);
            rowStringList.add(String.valueOf(getCellValue(row1.getCell(i))));
            headerList.add(rowStringList);
        }
        return compareLists(sourceHeaderList, headerList);
    }

    /**
     * list一个元素一行数据，map对应列
     * @param headerNameList
     * @param finalHeaderFieldList
     * @param valueList
     * @param recordTimeList
     * @return
     */
    private List<Map<String, Object>> mergeHeaderAndValuesToMaps(List<String> headerNameList, List<String> finalHeaderFieldList, List<List<Object>> valueList, List<String> recordTimeList) {
        List<Map<String, Object>> importDataList = new ArrayList<>();
        // 没有填完整数据需要补齐
        for (List<Object> list : valueList) {
            if (headerNameList.size() > list.size()) {
                int needAddSize = headerNameList.size() - list.size();
                list.addAll(new ArrayList<>(Collections.nCopies(needAddSize, "")));
            }
        }
        for (List<Object> objectList : valueList) {
            Map<String, Object> importData = new LinkedHashMap<>();
            for (int i = 0; i < headerNameList.size(); i++) {
                String headerField = finalHeaderFieldList.get(i);
                Object value = objectList.get(i);
                importData.put(headerField, value);
            }
            importDataList.add(importData);
        }
        //时间列
//        for (int i = 0; i < importDataList.size(); i++) {
//            Map<String, Object> dataMap = importDataList.get(i);
//            dataMap.put("timeOfFillInnerres", recordTimeList.get(i));
//        }
        return importDataList;
    }

    // 比较两个集合是否相等
    public boolean compareLists(List<List<String>> list1, List<List<String>> list2) {
        if (list1.size() != list2.size()) {
            return false;
        }
        for (int i = 0; i < list1.size(); i++) {
            List<String> sublist1 = list1.get(i);
            List<String> sublist2 = list2.get(i);

            if (sublist1.size() != sublist2.size()) {
                return false;
            }
            for (int j = 0; j < sublist1.size(); j++) {
                String value1 = sublist1.get(j);
                String value2 = sublist2.get(j);

                if (!value1.equals(value2)) {
                    return false;
                }
            }
        }
        return true;
    }

    private static Object getCellValue(CellValue cell) {
        if (cell == null) {
            return "";
        }
        CellType cellType = cell.getCellType();
        if (cellType == CellType.STRING) {
            return cell.getStringValue();
        } else if (cellType == CellType.NUMERIC) {
            return BigDecimal.valueOf(cell.getNumberValue()).stripTrailingZeros().toPlainString();
        } else if (cellType == CellType.BOOLEAN) {
            return cell.getBooleanValue();
        } else if (cellType == CellType.BLANK) {
            return ""; // 空单元格
        } else if (cellType == CellType.FORMULA){
            try {
                return cell.getStringValue();
            } catch (Exception e) {
                return "";
            }
        } else {
            return "";
        }
    }

    private static Object getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        CellType cellType = cell.getCellType();
        if (cellType == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cellType == CellType.NUMERIC) {
            return BigDecimal.valueOf(cell.getNumericCellValue()).toPlainString();
        } else if (cellType == CellType.BOOLEAN) {
            return cell.getBooleanCellValue();
        } else if (cellType == CellType.BLANK) {
            return ""; // 空单元格
        } else if (cellType == CellType.FORMULA){
            try {
                return cell.getStringCellValue();
            } catch (Exception e) {
                return "";
            }
        } else {
            return "";
        }
    }

    @Override
    public PageResult<DpvTestFailureRecord> listPage(DpvTestFailureRecord param) {
        LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(param.getProductName())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getProductName, param.getProductName());
        }
        if (ObjectUtil.isNotEmpty(param.getInitiatorName())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getInitiatorName, param.getInitiatorName());
        }
        if (ObjectUtil.isNotEmpty(param.getStockStatus())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getStockStatus, param.getStockStatus());
        }
        if (ObjectUtil.isNotEmpty(param.getCellCode())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getCellCode, param.getCellCode());
        }
        if (ObjectUtil.isNotEmpty(param.getFileCode())) {
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFileCode, param.getFileCode());
        }

        if (ObjectUtil.isNotEmpty(param.getReviewStatus())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getReviewStatus, param.getReviewStatus());
        }
        if (ObjectUtil.isNotEmpty(param.getProductDepartment())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getProductDepartment, param.getProductDepartment());
        }
        if (ObjectUtil.isNotEmpty(param.getLaboratoryId())) {
            dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getLaboratoryId, param.getLaboratoryId());
        }
        if (ObjectUtil.isNotEmpty(param.getTestProjectName())) {//测试项目名称
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getTestProjectName, param.getTestProjectName());
        }
        if (ObjectUtil.isNotEmpty(param.getFaChargeAccount())) {//FA责任人
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFaChargeAccount, param.getFaChargeAccount());
        }
        if (ObjectUtil.isNotEmpty(param.getFaChargeName())) {//FA责任人
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFaChargeName, param.getFaChargeName());
        }
        if (ObjectUtil.isNotEmpty(param.getFaStatusList())) {//FA状态
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getOverallFaStatus, param.getFaStatusList());
        }
        if (ObjectUtil.isNotEmpty(param.getTestTypeList())) {//试验类型
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getTestType, param.getTestTypeList());
        }
        if (ObjectUtil.isNotEmpty(param.getTestCateList())) {//测试大类
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getTestCate, param.getTestCateList());
        }
        if (ObjectUtil.isNotEmpty(param.getFailureCateList())) {//失效类别
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getFailureCate, param.getFailureCateList());
        }
        if (ObjectUtil.isNotEmpty(param.getStockStatusList())) {//库存状态
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getStockStatus, param.getStockStatusList());
        }
        if (ObjectUtil.isNotEmpty(param.getFolderDepartment())) {//委托部门
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getFolderDepartment, param.getFolderDepartment());
        }
        if (ObjectUtil.isNotEmpty(param.getTestSampleStage())) {//产品技术状态
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getTestSampleStage, param.getTestSampleStage());
        }
        if (ObjectUtil.isNotEmpty(param.getReviewStatusList())) {//审核状态
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getReviewStatus, param.getReviewStatusList());
        }
        if ("notNull".equals(param.getSampleStatus())) {
            dpvTestFailureRecordLambdaQueryWrapper.isNotNull(DpvTestFailureRecord::getSampleStatus);
        }
        if (ObjectUtil.isNotEmpty(param.getSampleStatusList())) {//样品状态
            dpvTestFailureRecordLambdaQueryWrapper.in(DpvTestFailureRecord::getSampleStatus, param.getSampleStatusList());
        }
        if (ObjectUtil.isNotEmpty(param.getSummary())) {//概要
            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getSummary, param.getSummary());
        }
        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        String userAccount = sysLoginUser.getAccount();
        if (ObjectUtil.isNotEmpty(param.getInitiatorAccount())) {
            if ("loginUser".equals(param.getInitiatorAccount())) {
                dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getInitiatorAccount, userAccount);
            }
        }
        dpvTestFailureRecordLambdaQueryWrapper.and(wrapper -> wrapper.isNull(DpvTestFailureRecord::getParentId).or().eq(DpvTestFailureRecord::getParentId, 1L));

        List<String> loginUserRoleIds = LoginContextHolder.me().getLoginUserRoleIds();
        // ”产品测试-测试失效-管理员“角色，ID:1781119467332612097，研发大数据部人员，可查看所有数据；其余只能查看与自己相关的数据
        if (!"superAdmin".equals(userAccount) && !loginUserRoleIds.contains("1781119467332612097")) {
            dpvTestFailureRecordLambdaQueryWrapper.and(wrapper -> {
                wrapper.like(DpvTestFailureRecord::getCountersigner, userAccount);
                for (SFunction<DpvTestFailureRecord, String> roleField : ROLE_FIELDS) {
                    wrapper.or().eq(roleField, userAccount);
                }
            });
        }

        dpvTestFailureRecordLambdaQueryWrapper.orderByDesc(DpvTestFailureRecord::getCreateTime);

        Page<DpvTestFailureRecord> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), dpvTestFailureRecordLambdaQueryWrapper);
        List<DpvTestFailureRecord> records = page.getRecords();
        List<Long> parentIdList = records.stream().map(DpvTestFailureRecord::getId).collect(Collectors.toList());
        //获取到委托单
        if (!parentIdList.isEmpty()) {//不为空时，获取子节点
            LambdaQueryWrapper<DpvTestFailureRecord> childrenLambdaQueryWrapper = new LambdaQueryWrapper<>();
            childrenLambdaQueryWrapper.in(DpvTestFailureRecord::getParentId, parentIdList);
            List<DpvTestFailureRecord> childrenList = this.list(childrenLambdaQueryWrapper);
            if ("noChildren".equals(param.getQueryType())) {
                // 处理电芯数据拼接
                Map<Long, List<DpvTestFailureRecord>> childrenListMap = childrenList.stream().collect(Collectors.groupingBy(DpvTestFailureRecord::getParentId));
                for (DpvTestFailureRecord parentRecord : records) {
                    Set<String> faIssueKeySet = new HashSet<>();
                    List<JSONObject> reportProcessList = new ArrayList<>();
                    String parentReportIssueKey = parentRecord.getFaAnalyseReportIssueKey();
                    if (StrUtil.isNotEmpty(parentReportIssueKey) && faIssueKeySet.add(parentReportIssueKey)) {
                        JSONObject reportProcessObj = new JSONObject();
                        reportProcessObj.put("faAnalyseReportIssueKey", parentReportIssueKey);
                        reportProcessObj.put("faStatus", parentRecord.getFaStatus());
                        reportProcessObj.put("faAnalysisReportId", parentRecord.getFaAnalysisReportId());
                        reportProcessObj.put("faAnalysisReportName", parentRecord.getFaAnalysisReportName());
                        reportProcessObj.put("faBreakReportId", parentRecord.getFaBreakReportId());
                        reportProcessObj.put("faBreakReportName", parentRecord.getFaBreakReportName());
                        reportProcessObj.put("causeAnalysis", parentRecord.getCauseAnalysis());
                        reportProcessObj.put("tempMeasures", parentRecord.getTempMeasures());
                        reportProcessObj.put("longTermMeasures", parentRecord.getLongTermMeasures());
                        reportProcessObj.put("resultVerification", parentRecord.getResultVerification());
                        reportProcessList.add(reportProcessObj);
                    }

                    int testSampleNum = 1;
                    List<Long> orderIdList = new ArrayList<>();
                    StringJoiner orderNoSj = new StringJoiner(",");
                    // 添加父级电芯
                    orderIdList.add(parentRecord.getOrderId());
                    String orderNo = StrUtil.isNotEmpty(parentRecord.getOrderNo()) ? parentRecord.getOrderNo() : parentRecord.getCellCode();
                    if (StrUtil.isNotEmpty(orderNo) && orderNo.contains("-")) {
                        String numStr = orderNo.split("-")[orderNo.split("-").length - 1];
                        try {
                            int num = Integer.parseInt(numStr);
                            orderNoSj.add(num + "");
                        } catch (NumberFormatException e) {
                            orderNoSj.add(numStr);
                            log.error(">>> 测试失效样品编号解析失败，id为：{}，orderNo为：{}，具体信息为：{}", param.getId(), param.getOrderNo(), e.getMessage());
                        }
                    } else {
                        orderNoSj.add(orderNo);
                    }
                    // 添加子级电芯
                    if (childrenListMap.containsKey(parentRecord.getId())) {
                        List<DpvTestFailureRecord> childrenRecords = childrenListMap.get(parentRecord.getId());
                        if (CollectionUtil.isNotEmpty(childrenRecords)) {
                            testSampleNum += childrenRecords.size();
                        }
                        orderIdList.addAll(childrenRecords.stream().map(DpvTestFailureRecord::getOrderId).collect(Collectors.toList()));
                        for (DpvTestFailureRecord childrenRecord : childrenRecords) {
                            String reportIssueKey = childrenRecord.getFaAnalyseReportIssueKey();
                            if (StrUtil.isNotEmpty(reportIssueKey) && faIssueKeySet.add(reportIssueKey)) {
                                JSONObject reportProcessObj = new JSONObject();
                                reportProcessObj.put("faAnalyseReportIssueKey", reportIssueKey);
                                reportProcessObj.put("faStatus", childrenRecord.getFaStatus());
                                reportProcessObj.put("faAnalysisReportId", childrenRecord.getFaAnalysisReportId());
                                reportProcessObj.put("faAnalysisReportName", childrenRecord.getFaAnalysisReportName());
                                reportProcessObj.put("faBreakReportId", childrenRecord.getFaBreakReportId());
                                reportProcessObj.put("faBreakReportName", childrenRecord.getFaBreakReportName());
                                reportProcessObj.put("causeAnalysis", childrenRecord.getCauseAnalysis());
                                reportProcessObj.put("tempMeasures", childrenRecord.getTempMeasures());
                                reportProcessObj.put("longTermMeasures", childrenRecord.getLongTermMeasures());
                                reportProcessObj.put("resultVerification", childrenRecord.getResultVerification());
                                reportProcessList.add(reportProcessObj);
                            }

                            String chilOrderNo = StrUtil.isNotEmpty(childrenRecord.getOrderNo()) ? childrenRecord.getOrderNo() : childrenRecord.getCellCode();
                            if (StrUtil.isNotEmpty(chilOrderNo) && chilOrderNo.contains("-")) {
                                String numStr = chilOrderNo.split("-")[chilOrderNo.split("-").length - 1];
                                try {
                                    int num = Integer.parseInt(numStr);
                                    orderNoSj.add(num + "");
                                } catch (NumberFormatException e) {
                                    orderNoSj.add(numStr);
                                    log.error(">>> 测试失效样品编号解析失败，id为：{}，orderNo为：{}，具体信息为：{}", param.getId(), param.getOrderNo(), e.getMessage());
                                }
                            } else {
                                orderNoSj.add(chilOrderNo);
                            }
                        }
                    }

                    parentRecord.setOrderIdList(orderIdList);
                    parentRecord.setOrderNos(orderNoSj.toString());
                    parentRecord.setTestSampleNum(testSampleNum);

                    // 处理分析报告流程key列表转换
                    List<JSONObject> sortedList = reportProcessList.stream()
                            .sorted(Comparator.comparingInt(item -> Integer.parseInt(item.getString("faAnalyseReportIssueKey").split("-")[1])))
                            .collect(Collectors.toList());
                    parentRecord.setFaAnalyseReportIssueKey(JSON.toJSONString(sortedList));

                }
            } else {
                //获取到委托单
                records.forEach(record -> {
//            DpvTestFailureRecord dpvTestFailureRecord = new DpvTestFailureRecord();
//            BeanUtil.copyProperties(record, dpvTestFailureRecord);
//            dpvTestFailureRecord.setId(Long.valueOf(record.getFileId()));
//            record.setParentId(1L);
//            record.setChildren(Arrays.asList(dpvTestFailureRecord));
                    List<DpvTestFailureRecord> collect = childrenList.stream().filter(children -> children.getParentId().equals(record.getId())).collect(Collectors.toList());
                    record.setChildren(collect);
                });
            }
            page.setRecords(records);
        }
        return new PageResult<>(page);

    }

    @Override
    public Map treeData(DpvTestFailureRecord param) {
        Map result = new HashMap();

        LambdaQueryWrapper<DpvTestFailureRecord> dpvTestFailureRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        dpvTestFailureRecordLambdaQueryWrapper.eq(DpvTestFailureRecord::getReviewStatus,"2");
        if (StrUtil.isNotBlank(param.getFileName())) {
//            dpvTestFailureRecordLambdaQueryWrapper.like(DpvTestFailureRecord::getProductName, param.getProductName());

            dpvTestFailureRecordLambdaQueryWrapper.and(wrapper ->
                    wrapper.like(DpvTestFailureRecord::getFileName,param.getFileName()).
                            or().like(DpvTestFailureRecord::getFaAnalysisReportName, param.getFileName()).
                            or().like(DpvTestFailureRecord::getFaBreakReportName, param.getFileName())
            );

        }

        dpvTestFailureRecordLambdaQueryWrapper.and(wrapper -> wrapper.isNull(DpvTestFailureRecord::getParentId).or().eq(DpvTestFailureRecord::getParentId, 1L));

        SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
        String userAccount = sysLoginUser.getAccount();
        List<String> loginUserRoleIds = LoginContextHolder.me().getLoginUserRoleIds();
        // ”产品测试-测试失效-管理员“角色，ID:1781119467332612097，研发大数据部人员，可查看所有数据；其余只能查看与自己相关的数据
        if (!"superAdmin".equals(userAccount) && !loginUserRoleIds.contains("1781119467332612097")) {
            dpvTestFailureRecordLambdaQueryWrapper.and(wrapper -> {
                wrapper.like(DpvTestFailureRecord::getCountersigner, userAccount);
                for (SFunction<DpvTestFailureRecord, String> roleField : ROLE_FIELDS) {
                    wrapper.or().eq(roleField, userAccount);
                }
            });
        }

        dpvTestFailureRecordLambdaQueryWrapper.orderByDesc(DpvTestFailureRecord::getCreateTime);

        List<DpvTestFailureRecord> records = this.list(dpvTestFailureRecordLambdaQueryWrapper);
        List<Long> parentIdList = records.stream().map(DpvTestFailureRecord::getId).collect(Collectors.toList());

        List<Map> treeDataList = new ArrayList<>();

        if (!parentIdList.isEmpty()) {//不为空时，获取子节点
            LambdaQueryWrapper<DpvTestFailureRecord> childrenLambdaQueryWrapper = new LambdaQueryWrapper<>();
            childrenLambdaQueryWrapper.in(DpvTestFailureRecord::getParentId, parentIdList);
            List<DpvTestFailureRecord> childrenList = this.list(childrenLambdaQueryWrapper);

            for (DpvTestFailureRecord failureRecord:records){
                Map firstNode = new HashMap();
                firstNode.put("code",failureRecord.getFileCode());
                firstNode.put("id",failureRecord.getId());
                //失效告知书
                firstNode.put("fileId",failureRecord.getFileId());
                firstNode.put("fileName",failureRecord.getFileName());
                if (StrUtil.isNotEmpty(failureRecord.getFileName()) && failureRecord.getFileName().contains(".")) {
                    firstNode.put("fileSuffix",failureRecord.getFileName().split("\\.")[failureRecord.getFileName().split("\\.").length -1]);
                }

                List<Map> cellFileList = new ArrayList<>();


                //先隐藏电芯维度报告
                /*Map cellData = new HashMap<>();
                cellData.put("code",failureRecord.getCellCode());
                List<Map> fileList = new ArrayList<>();
                //拆解报告
                if(StrUtil.isNotBlank(failureRecord.getFaBreakReportName())){
                    Map breakReport = new HashMap<>();
                    breakReport.put("fileId",failureRecord.getFaBreakReportId());
                    breakReport.put("fileName",failureRecord.getFaBreakReportName());
                    breakReport.put("fileSuffix",failureRecord.getFaBreakReportName().split("\\.")[failureRecord.getFaBreakReportName().split("\\.").length -1]);
                    fileList.add(breakReport);
                }
                //分析报告
                if(StrUtil.isNotBlank(failureRecord.getFaAnalysisReportName())){
                    Map analysisReport = new HashMap<>();
                    analysisReport.put("fileId",failureRecord.getFaAnalysisReportId());
                    analysisReport.put("fileName",failureRecord.getFaAnalysisReportName());
                    analysisReport.put("fileSuffix",failureRecord.getFaAnalysisReportName().split("\\.")[failureRecord.getFaAnalysisReportName().split("\\.").length -1]);
                    fileList.add(analysisReport);
                }
                cellData.put("data",fileList);
                cellFileList.add(cellData);

                List<DpvTestFailureRecord> otherCell = childrenList.stream().filter(children -> children.getParentId().equals(failureRecord.getId())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(otherCell)){
                    for (DpvTestFailureRecord other:otherCell){
                        Map otherData = new HashMap<>();
                        otherData.put("code",other.getCellCode());
                        List<Map> otherfileList = new ArrayList<>();
                        //拆解报告
                        if(StrUtil.isNotBlank(other.getFaBreakReportName())){
                            Map breakReport = new HashMap<>();
                            breakReport.put("fileId",other.getFaBreakReportId());
                            breakReport.put("fileName",other.getFaBreakReportName());
                            breakReport.put("fileSuffix",other.getFaBreakReportName().split("\\.")[other.getFaBreakReportName().split("\\.").length -1]);

                            otherfileList.add(breakReport);
                        }
                        //分析报告
                        if(StrUtil.isNotBlank(other.getFaAnalysisReportName())){
                            Map analysisReport = new HashMap<>();
                            analysisReport.put("fileId",other.getFaAnalysisReportId());
                            analysisReport.put("fileName",other.getFaAnalysisReportName());
                            analysisReport.put("fileSuffix",other.getFaAnalysisReportName().split("\\.")[other.getFaAnalysisReportName().split("\\.").length -1]);
                            otherfileList.add(analysisReport);
                        }
                        otherData.put("data",otherfileList);

                        cellFileList.add(otherData);
                    }
                }*/

                firstNode.put("cellFileList",cellFileList);

                treeDataList.add(firstNode);
            }


        }


        result.put("total",records.size());
        result.put("finished",records.stream().filter(l -> "finish".equals(l.getOverallFaStatus())).count());
        //beStart 待启动 onGoing
        result.put("ongoing",records.size()-records.stream().filter(l -> "finish".equals(l.getOverallFaStatus())).count());
        result.put("tree",treeDataList);
        return result;

    }
}
